#include "interface/JoyStick.h"

namespace {
const float TRIGGER_THRESHOLD = -0.9f;

UserCommand checkCmd(const std::vector<float> &axes,
                     const std::vector<int> &buttons) {

  const auto &l2 = axes[2];
  const auto &a = buttons[0];
  const auto &b = buttons[1];
  const auto &x = buttons[2];
  const auto &y = buttons[3];
  const auto &l1 = buttons[4];
  const auto &start = buttons[7];
  if (l2 < TRIGGER_THRESHOLD) {
    if (a == 1) {
      ROS_INFO("JoyStick::L2_A");
      return UserCommand::L2_A;
    }
    if (b == 1) {
      ROS_INFO("JoyStick::L2_B");
      return UserCommand::L2_B;
    }
    if (x == 1) {
      ROS_INFO("JoyStick::L2_X");
      return UserCommand::L2_X;
    }
  }
  if (start == 1) {
    ROS_INFO("JoyStick::START");
    return UserCommand::START;
  }
  if (l1 == 1) {
    if (x == 1) {
      ROS_INFO("JoyStick::L1_X");
      return UserCommand::L1_X;
    }
    if (y == 1) {
      ROS_INFO("JoyStick::L1_Y");
      return UserCommand::L1_Y;
    }
    if (a == 1) {
      ROS_INFO("JoyStick::L1_A");
      return UserCommand::L1_A;
    }
  }
  return UserCommand::NONE;
}
} // namespace

JoyStick::JoyStick() {
  userCmd = UserCommand::NONE;
  userValue.setZero();

  joy_sub_ = nh_.subscribe("/joy", 10, &JoyStick::joyCallback, this);
}

JoyStick::~JoyStick() {}

void JoyStick::joyCallback(const sensor_msgs::Joy::ConstPtr &joy_msg) {
  if (!joy_msg) {
    return;
  }

  const auto &axes = joy_msg->axes;
  const auto &buttons = joy_msg->buttons;

  userValue.lx = axes[0];
  userValue.ly = axes[1];
  userValue.L2 = axes[2];
  userValue.rx = axes[3];
  userValue.ry = axes[4];

  userCmd = checkCmd(axes, buttons);
}
