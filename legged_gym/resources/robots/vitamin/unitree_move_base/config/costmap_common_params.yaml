obstacle_range: 1.0         ##
raytrace_range: 1.5         ##
footprint: [[0.3, 0.15], [0.3, -0.15], [-0.35, -0.15], [-0.35, 0.15]]
# robot_radius: 0.3
inflation_radius: 0.03
max_obstacle_height: 1.0
min_obstacle_height: 0.0
# observation_sources: scan
# scan: {data_type: LaserScan, topic: /merged_laserscan, marking: true, clearing: true, expected_update_rate: 3.3}
observation_sources: faceLaserScan leftLaserScan rightLaserScan
faceLaserScan: {data_type: LaserScan, topic: /faceLaserScan, marking: true, clearing: true, expected_update_rate: 3.3}
leftLaserScan: {data_type: LaserS<PERSON>, topic: /leftLaserScan, marking: true, clearing: true, expected_update_rate: 3.3}
rightLaserScan: {data_type: LaserScan, topic: /rightLaserScan, marking: true, clearing: true, expected_update_rate: 3.3}
