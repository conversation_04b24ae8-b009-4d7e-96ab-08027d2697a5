<launch>
    <!-- transform pointCloud2 to laserScan -->
    <node pkg="pointcloud_to_laserscan" type="pointcloud_to_laserscan_node" name="face_pointcloud_to_laserscan" 
        respawn="false" output="screen">
        <remap from="cloud_in" to="/cam1/point_cloud_face"/>
        <remap from="/scan" to="/faceLaserScan"/>
        <rosparam>
            target_frame: camera_face
            transform_tolerance: 0.01
            min_height: -0.05
            max_height: 0.1

            angle_min: -0.087
            angle_max: 0.087
            angle_increment: 0.0175
            scan_time: 0.3333
            range_min: 0.1
            range_max: 1.5
            use_inf: true

            # Concurrency level, affects number of pointclouds queued for processing and number of threads used
            # 0 : Detect number of cores
            # 1 : Single threaded
            # 2->inf : Parallelism level
            concurrency_level: 1
        </rosparam>
        <!-- <rosparam file="$(find unitree_move_base)/config/pointCloud_to_laserScan_params.yaml" command="load" /> -->
    </node>

    <node pkg="pointcloud_to_laserscan" type="pointcloud_to_laserscan_node" name="left_pointcloud_to_laserscan" 
        respawn="false" output="screen">
        <remap from="cloud_in" to="/cam3/point_cloud_left"/>
        <remap from="/scan" to="/leftLaserScan"/>
        <rosparam>
            target_frame: camera_laserscan_link_left
            transform_tolerance: 0.01
            min_height: -0.05
            max_height: 0.1

            angle_min: -0.087
            angle_max: 0.087
            angle_increment: 0.0175
            scan_time: 0.3333
            range_min: 0.1
            range_max: 1.5
            use_inf: true

            # Concurrency level, affects number of pointclouds queued for processing and number of threads used
            # 0 : Detect number of cores
            # 1 : Single threaded
            # 2->inf : Parallelism level
            concurrency_level: 1
        </rosparam>
        <!-- <rosparam file="$(find unitree_move_base)/config/pointCloud_to_laserScan_params.yaml" command="load" /> -->
    </node>

    <node pkg="pointcloud_to_laserscan" type="pointcloud_to_laserscan_node" name="right_pointcloud_to_laserscan" 
        respawn="false" output="screen">
        <remap from="cloud_in" to="/cam4/point_cloud_right"/>
        <remap from="/scan" to="/rightLaserScan"/>
        <rosparam>
            target_frame: camera_laserscan_link_right
            transform_tolerance: 0.01
            min_height: -0.05
            max_height: 0.1

            angle_min: -0.087
            angle_max: 0.087
            angle_increment: 0.0175
            scan_time: 0.3333
            range_min: 0.1
            range_max: 1.5
            use_inf: true

            # Concurrency level, affects number of pointclouds queued for processing and number of threads used
            # 0 : Detect number of cores
            # 1 : Single threaded
            # 2->inf : Parallelism level
            concurrency_level: 1
        </rosparam>
        <!-- <rosparam file="$(find unitree_move_base)/config/pointCloud_to_laserScan_params.yaml" command="load" /> -->
    </node>

</launch>