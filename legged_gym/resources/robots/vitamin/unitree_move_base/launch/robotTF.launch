<launch>
    <arg name="user_debug" default="false"/>

    <param name="robot_description" command="$(find xacro)/xacro --inorder '$(find go1_description)/xacro/robot.xacro'
            DEBUG:=$(arg user_debug)"/>

    <!-- convert joint states to TF transforms for rviz, etc -->
    <node pkg="robot_state_publisher" type="robot_state_publisher" name="robot_state_publisher"
          respawn="false" output="screen">
        <remap from="/joint_states" to="/realRobot/joint_states"/>
    </node>
</launch>