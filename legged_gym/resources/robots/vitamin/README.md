# Vitamin

## 简介

Vitamin 基于[unitree_guide](./README.md) 构建 Vita 四足机器狗的基本控制软件框架

## 依赖

## ROS2

https://docs.ros.org/en/humble/Installation/Ubuntu-Install-Debs.html

## 编译

```sh
make build
```

## 运行

```sh
make sim
```

## 配置

```sh
make cfg
```

- `robot`: 机器人本体名称，默认是 `vita00`，可选 `vita00w`, `go2`。
- `robot_scene`: 场景文件，默认是 `scene.xml`，可选 `scene_with_ghost.xml`，可开启 ghost 模式。
- `joystick_topic`: 手柄话题，默认是 `/joy`。
- `low_cmd_topic`: 低级控制话题，默认是 `/sim/lowcmd`。
- `low_state_topic`: 低级状态话题，默认是 `/sim/lowstate`。
- `limit_pause`: 关节超限时是否暂停仿真

## him loco 模型

拉取所有子模块

```sh
git submodule update --init --recursive
```

进入 run 环境之后，可以按照以下方法跑闭环仿真:

```sh
cd docker
just run your_vnc_port your_bridge_port
make himloco
```

如果使用虚拟的 joystick，可以使用:

```sh
make joystick
```

如果使用手机 App 来控制：

```sh
ros2 run foxglove_bridge foxglove_bridge
```

## 工具


### VitaFlow

VitaFlow 是 Vita 的数据流管理工具，支持数据记录和回放。

```sh
make vitaflow /home/<USER>/Data/rosbag2_2025_03_15-16_19_36_0.mcap
```

这样会自动回放这个 mcap 文件，并发给 `vita_mujoco`，并将仿真返回的数据以及原始的数据都录制到一个新的 mcap 中。

### JoyStick 工具

如果手头有一个手柄，且可以接入到 simulator 所在的 PC 上，那是最好的，控制起来最顺滑。
但对于远程访问的同学来说可能就有些不便了。

这里提供一个虚拟手柄，供大家使用。

```sh
cd src/unittest
uv venv
uv pip install pygame
export PYTHONPATH=/YOUR_PATH/src/unittest/.venv/lib/python3.10/site-packages/:$PYTHONPATH
export ROS_DOMAIN_ID=42 # 你的 ID
python3 joystick.py
```

这个工具也支持 XWindows 转发，所以只要开了 XQuartz，且使用 `ssh -X/-Y` 登陆服务器，可以直接显示出来，但速度响应比较慢。
更推荐在 simulator 的机器上直接使用，通过向日葵等手段远程访问。

## 注意

### 消息类型的支持

`vita_sim` 同时支持两种消息格式的通讯，默认采用 VITA 的 `lowlevel_msg` 消息定义，它对于 `unitree_go` 的消息定义有所增补
如需切换到 `unitree_go`（如希望复现 [unittest](src/unittest/stand_go2.py)），那么请重新编译 `vita_sim` 包并增加开关设定(`USE_UNITREE_GO=ON`)，如下:

```sh
colcon build --packages-select vita_sim --cmake-args -DUSE_UNITREE_GO=ON --event-handlers console_direct+
```
