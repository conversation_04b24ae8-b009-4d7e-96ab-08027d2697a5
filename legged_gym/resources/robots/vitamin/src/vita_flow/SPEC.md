# Specification

## 回灌录制操作

最简单的回灌录制方式：

```sh
ros2 launch vita_flow flow.launch.py bag_path:='/path/to/bag_file.mcap'
```

这个命令会同时启动播放节点和录制节点，播放指定的bag文件并录制相关话题。

## 环境变量配置（高级用法）

如需自定义配置，可通过环境变量：

```sh
# 设置录制存储路径
export VITA_STORAGE_PATH="/mnt/ramdisk/"

# 设置录制话题列表
export VITA_RECORDER_TOPICS="/sportmodestate,/lowstate,/rt/odom,/lio_sam/mapping/odometry"

# 设置播放话题列表
export VITA_PLAYER_TOPICS="/sportmodestate,/lowstate"

# 设置播放速率
export VITA_PLAYER_RATE="1.0"

# 启动
ros2 launch vita_flow flow.launch.py bag_path:='/path/to/bag_file.mcap'
```

## 单独的录制与回放

### 单独录制

```sh
ros2 run vita_flow record
```

默认录制话题列表：`/sim/lowcmd,/sim/lowstate,/rl_lowcmd,/rt/lowstate`

### 单独回放

```sh
ros2 run vita_flow play /path/to/bag_file.mcap
```

默认回放话题列表：`/sim/lowcmd,/rl_lowcmd,/rt/lowstate`

## 退出机制

recorder节点在收到stop命令后会自动停止录制并退出。当与player节点配合使用时，player节点播放完成后会自动发送stop命令给recorder节点，导致录制完成并自动退出。
