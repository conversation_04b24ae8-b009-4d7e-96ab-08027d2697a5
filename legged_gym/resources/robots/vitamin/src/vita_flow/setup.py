from setuptools import setup
import os
from glob import glob

package_name = "vita_flow"

setup(
    name=package_name,
    version="0.0.1",
    packages=[package_name],
    data_files=[
        ("share/ament_index/resource_index/packages", ["resource/" + package_name]),
        ("share/" + package_name, ["package.xml"]),
        (os.path.join("share", package_name, "launch"), glob("launch/*.launch.py")),
        (os.path.join("share", package_name, "resource"), glob("resource/*.yml")),
    ],
    install_requires=["setuptools"],
    extras_require={
        "test": ["pytest"],
    },
    zip_safe=True,
    maintainer="peizhe.chen",
    maintainer_email="<EMAIL>",
    description="ROS2 data flow management package for recording and playing back data",
    license="MIT",
    entry_points={
        "console_scripts": [
            "record = vita_flow.recorder_node:main",
            "play = vita_flow.player_node:main",
        ],
    },
)
