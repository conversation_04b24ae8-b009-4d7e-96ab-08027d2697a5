#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sim_msg.srv import Control
from rosbag2_py import RecordOptions, StorageOptions, Recorder
import os
import signal
from datetime import datetime
from typing import Optional
from ament_index_python.packages import get_package_share_directory
import threading
import time


class RecorderNode(Node):
    def __init__(self):
        super().__init__("record")

        # 获取默认配置文件路径
        default_config = os.path.join(
            get_package_share_directory("vita_flow"),
            "resource",
            "mcap_writer_options.yml",
        )

        self.declare_parameters(
            namespace="",
            parameters=[
                (
                    "topics",
                    "/sim/lowcmd,/sim/lowstate,/rl_lowcmd,/rt/lowstate",
                ),
                ("storage_path", "/mnt/ramdisk/"),
                ("mcap_config_path", default_config),
            ],
        )

        self.srv = self.create_service(
            Control, "control_recording", self.handle_control
        )

        self.recorder: Optional[Recorder] = None
        self.recording: bool = False
        self.current_bag_path: str = ""
        self._exit_flag: bool = False  # 用于标记节点是否应该退出

        # 立即获取存储路径参数值并打印
        storage_path = self.get_parameter("storage_path").value
        self.get_logger().info(f"[配置] 存储路径: {storage_path}")
        self.get_logger().info("[准备就绪] Recorder节点已初始化")

    def handle_control(self, request, response):
        try:
            if request.command == "start" and not self.recording:
                self.get_logger().info("[命令] 收到开始录制命令")
                self.start_recording()
                response.success = True
            elif request.command == "stop" and self.recording:
                self.get_logger().info("[命令] 收到停止录制命令")
                self.stop_recording()
                response.success = True

                # 收到stop命令后，设置退出标志并启动一个直接退出的定时器
                self.get_logger().info("[退出] 停止录制后准备退出节点")
                self._exit_flag = True

                # 启动一个定时器，在短暂延迟后直接终止进程
                def force_exit():
                    self.get_logger().info("[退出] 强制退出进程")
                    # 使用os._exit直接终止进程，跳过所有清理流程
                    os._exit(0)

                # 设置一个定时器，1秒后强制退出
                exit_timer = threading.Timer(1.0, force_exit)
                exit_timer.daemon = True
                exit_timer.start()
            else:
                if request.command not in ["start", "stop"]:
                    self.get_logger().warn(f"[警告] 无效命令: {request.command}")
                else:
                    self.get_logger().warn(
                        f"[警告] 无效状态: 命令={request.command}, 当前录制状态={self.recording}"
                    )
                response.success = False
        except Exception as e:
            self.get_logger().error(f"[错误] 处理控制请求: {str(e)}")
            response.success = False

        return response

    def start_recording(self):
        try:
            self.get_logger().info("[配置] 获取录制参数")
            topics_str = self.get_parameter("topics").value
            base_path = self.get_parameter("storage_path").value
            mcap_config = self.get_parameter("mcap_config_path").value

            # 将字符串形式的topics拆分为列表
            if isinstance(topics_str, str):
                topics = topics_str.split(",")
            else:
                topics = topics_str

            # 添加参数检查和路径规范化
            if not base_path:
                self.get_logger().warn("[警告] 存储路径为空，使用默认值: /mnt/ramdisk/")
                base_path = "/mnt/ramdisk/"
            if not base_path.endswith("/"):
                base_path += "/"

            # 确保目录存在
            try:
                os.makedirs(base_path, exist_ok=True)
            except Exception as e:
                self.get_logger().error(f"[错误] 创建目录失败: {str(e)}")
                base_path = "/tmp/vita_flow/"
                os.makedirs(base_path, exist_ok=True)
                self.get_logger().warn(f"[警告] 使用备用目录: {base_path}")

            # 创建录制文件路径
            timestamp = datetime.now().strftime("%Y_%m_%d-%H_%M_%S")
            self.current_bag_path = os.path.join(base_path, f"recording_{timestamp}")
            self.get_logger().info(f"[路径] 录制文件: {self.current_bag_path}")

            # 创建存储选项
            storage_options = StorageOptions(
                uri=self.current_bag_path,
                storage_id="mcap",
                storage_config_uri=mcap_config,
            )

            # 设置记录选项
            self.get_logger().info(f"[话题] 录制话题: {', '.join(topics)}")
            record_options = RecordOptions()
            record_options.all = len(topics) == 1 and topics[0] == "*"
            record_options.topics = (
                topics if not (len(topics) == 1 and topics[0] == "*") else []
            )

            # 创建录制器
            self.recorder = Recorder()
            self.recording = True

            # 在后台线程中启动录制
            def record_thread():
                try:
                    self.get_logger().info("[启动] 开始录制")
                    self.recorder.record(storage_options, record_options)
                except Exception as e:
                    if self.recording:  # 只有在不是主动停止的情况下才报错
                        self.get_logger().error(f"[错误] 录制失败: {str(e)}")
                    self.recording = False
                    self.recorder = None

            self._record_thread = threading.Thread(target=record_thread)
            self._record_thread.daemon = True
            self._record_thread.start()

            self.get_logger().info(f"[成功] 开始录制到 {self.current_bag_path}")

        except Exception as e:
            self.get_logger().error(f"[错误] 启动录制失败: {str(e)}")
            self.recorder = None
            self.recording = False
            self.current_bag_path = ""
            raise

    def stop_recording(self):
        try:
            if not self.recorder or not self.recording:
                self.get_logger().warn("[警告] 没有正在进行的录制")
                return

            self.get_logger().info("[处理] 停止录制")

            # 设置标志位
            self.recording = False

            # 等待录制线程结束
            if hasattr(self, "_record_thread") and self._record_thread.is_alive():
                self._record_thread.join(timeout=5.0)  # 最多等待5秒

            # 清理资源
            file_path = self.current_bag_path
            self.recorder = None
            self.current_bag_path = ""

            self.get_logger().info(f"[成功] 录制已停止并保存到: {file_path}")

        except Exception as e:
            self.get_logger().error(f"[错误] 停止录制失败: {str(e)}")
            raise


def main(args=None):
    rclpy.init(args=args)
    node = RecorderNode()

    node.get_logger().info("[等待] 等待录制控制命令")

    try:
        # 使用简单循环来检查退出标志
        while rclpy.ok():
            rclpy.spin_once(node, timeout_sec=0.1)
            # 检查是否应该退出
            if hasattr(node, "_exit_flag") and node._exit_flag:
                node.get_logger().info("[退出] 收到退出标志，准备关闭节点")
                break
    except KeyboardInterrupt:
        node.get_logger().info("[中断] 接收到中断信号")
    except Exception as e:
        node.get_logger().error(f"[错误] 节点运行错误: {str(e)}")
    finally:
        if node.recording:
            node.get_logger().info("[清理] 停止录制")
            try:
                node.stop_recording()
            except Exception as e:
                node.get_logger().error(f"[错误] 清理录制失败: {str(e)}")

        node.get_logger().info("[退出] 销毁节点")
        try:
            node.destroy_node()
        except Exception as e:
            node.get_logger().error(f"[错误] 销毁节点失败: {str(e)}")

        try:
            # 先向自己发送SIGINT信号，这可能会帮助解锁ROS2的资源
            node.get_logger().info("[退出] 发送SIGINT信号给自己")
            pid = os.getpid()
            os.kill(pid, signal.SIGINT)
            
            # 短暂等待让信号处理器有时间工作
            time.sleep(0.1)
            
            # 然后尝试正常关闭
            node.get_logger().info("[退出] 关闭ROS2上下文")
            rclpy.shutdown()
        except Exception as e:
            node.get_logger().error(f"[错误] 关闭ROS2失败: {str(e)}")
            # 最后的解决方案，强制退出
            node.get_logger().info("[退出] 强制退出进程")
            os._exit(0)


if __name__ == "__main__":
    main()
