<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from vita00w.xacro                   | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="vita00w">
  <mujoco>
    <compiler balanceinertia="true" discardvisual="false" meshdir="../meshes/"/>
  </mujoco>
  <link name="base">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00775 0.0 0.0"/>
      <mass value="7.119"/>
      <inertia ixx="28807.764E-06" ixy="-676.862E-06" ixz="1592.964E-06" iyy="86135.021E-06" iyz="-232.235E-06" izz="100529.859E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/trunk.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="1.57079 0 1.57079" xyz="0.29866 0 0.0215"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/Gemini_335.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="1.57079 -0.2618 3.1415926" xyz="0.22454 0 0.07165"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/Livox_mid360.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.075"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/orinNX.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.32 0.18 0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.075"/>
      <geometry>
        <box size="0.089 0.118 0.04"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.12 0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.12 -0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.12 -0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.12 0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.23 0 0.008"/>
      <geometry>
        <box size="0.13 0.1 0.11"/>
      </geometry>
    </collision>
  </link>
  <link name="FR_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00249 -0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="1.271e-05" ixz="0.0" iyy="293.236E-06" iyz="-0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.206 -0.05 0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.4538" upper="0.6632" velocity="37.7"/>
  </joint>
  <link name="FR_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00615 0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="0.000105" ixz="0.00045" iyy="6140E-06" iyz="0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/thigh_right.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.73 0" xyz="-0.029 0 -0.242"/>
      <geometry>
        <box size="0.002 0.02 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 -0.015 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 0.014 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0  -0.0893 0.0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="3.14159" velocity="37.7"/>
  </joint>
  <link name="FR_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0037488 -0.004252 -0.14857"/>
      <mass value="0.31147"/>
      <inertia ixx="0.0020884" ixy="2.8427E-06" ixz="-4.0254E-05" iyy="0.0022072" iyz="-7.607E-05" izz="0.0001761"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_right_calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.89804 0.91765 0.92941 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.15 0" xyz="0.013 0 -0.085"/>
      <geometry>
        <cylinder length="0.16" radius="0.01"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 -0.21"/>
      <geometry>
        <cylinder length="0.04" radius="0.04"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.7158" velocity="21.8"/>
  </joint>
  <link name="FR_wheel">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0013575 -0.022348 0.022244"/>
      <mass value="0.86014"/>
      <inertia ixx="0.0044582" ixy="4.1515E-05" ixz="-9.6516E-05" iyy="0.0048298" iyz="0.00056141" izz="0.001609"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_right_wheel.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.07"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_wheel_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0499 -0.21"/>
    <parent link="FR_calf"/>
    <child link="FR_wheel"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" velocity="37.7"/>
  </joint>
  <link name="FL_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00249 0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="-1.271e-05" ixz="0.0" iyy="293.236E-06" iyz="0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.206 0.05 0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.6632" upper="0.4538" velocity="37.7"/>
  </joint>
  <link name="FL_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00615 -0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="-0.000105" ixz="0.00045" iyy="6140E-06" iyz="-0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/thigh_left.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.73 0" xyz="-0.029 0 -0.242"/>
      <geometry>
        <box size="0.002 0.02 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 -0.015 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 0.014 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0  0.0893 0.0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="3.14159" velocity="37.7"/>
  </joint>
  <link name="FL_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0037488 -0.004252 -0.14857"/>
      <mass value="0.31147"/>
      <inertia ixx="0.0020884" ixy="2.8427E-06" ixz="-4.0254E-05" iyy="0.0022072" iyz="-7.607E-05" izz="0.0001761"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_left_calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.89804 0.91765 0.92941 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.15 0" xyz="0.013 0 -0.085"/>
      <geometry>
        <cylinder length="0.16" radius="0.01"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 -0.21"/>
      <geometry>
        <cylinder length="0.04" radius="0.04"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.7158" velocity="21.8"/>
  </joint>
  <link name="FL_wheel">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0013575 -0.022348 0.022244"/>
      <mass value="0.86014"/>
      <inertia ixx="0.0044582" ixy="4.1515E-05" ixz="-9.6516E-05" iyy="0.0048298" iyz="0.00056141" izz="0.001609"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_left_wheel.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.003 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.07"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_wheel_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0499 -0.21"/>
    <parent link="FL_calf"/>
    <child link="FL_wheel"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" velocity="37.7"/>
  </joint>
  <link name="RR_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00249 -0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="-1.271e-05" ixz="-0.0" iyy="293.236E-06" iyz="-0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.206 -0.05 0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.4538" upper="0.6632" velocity="37.7"/>
  </joint>
  <link name="RR_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00615 0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="-0.000105" ixz="-0.00045" iyy="6140E-06" iyz="0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/thigh_right.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.73 0" xyz="-0.029 0 -0.242"/>
      <geometry>
        <box size="0.002 0.02 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 -0.015 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 0.014 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0  -0.0893 0.0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="4.1713" velocity="37.7"/>
  </joint>
  <link name="RR_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0037488 -0.004252 -0.14857"/>
      <mass value="0.31147"/>
      <inertia ixx="0.0020884" ixy="2.8427E-06" ixz="-4.0254E-05" iyy="0.0022072" iyz="-7.607E-05" izz="0.0001761"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_right_calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.89804 0.91765 0.92941 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.15 0" xyz="0.013 0 -0.085"/>
      <geometry>
        <cylinder length="0.16" radius="0.01"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 -0.21"/>
      <geometry>
        <cylinder length="0.04" radius="0.04"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.7299" velocity="21.8"/>
  </joint>
  <link name="RR_wheel">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0013575 -0.022348 0.022244"/>
      <mass value="0.86014"/>
      <inertia ixx="0.0044582" ixy="4.1515E-05" ixz="-9.6516E-05" iyy="0.0048298" iyz="0.00056141" izz="0.001609"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_right_wheel.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.07"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_wheel_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 -0.0499 -0.21"/>
    <parent link="RR_calf"/>
    <child link="RR_wheel"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" velocity="37.7"/>
  </joint>
  <link name="RL_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00249 0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="1.271e-05" ixz="-0.0" iyy="293.236E-06" iyz="0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.046"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.206 0.05 0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.6632" upper="0.4538" velocity="37.7"/>
  </joint>
  <link name="RL_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00615 -0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="0.000105" ixz="-0.00045" iyy="6140E-06" iyz="-0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/thigh_left.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.73 0" xyz="-0.029 0 -0.242"/>
      <geometry>
        <box size="0.002 0.02 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 -0.015 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="-0.012 0.014 -0.225"/>
      <geometry>
        <cylinder length="0.004" radius="0.027"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0  0.0893 0.0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="4.1713" velocity="37.7"/>
  </joint>
  <link name="RL_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0037488 -0.004252 -0.14857"/>
      <mass value="0.31147"/>
      <inertia ixx="0.0020884" ixy="2.8427E-06" ixz="-4.0254E-05" iyy="0.0022072" iyz="-7.607E-05" izz="0.0001761"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_left_calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.89804 0.91765 0.92941 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.15 0" xyz="0.013 0 -0.085"/>
      <geometry>
        <cylinder length="0.16" radius="0.01"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0 -0.21"/>
      <geometry>
        <cylinder length="0.04" radius="0.04"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.7299" velocity="21.8"/>
  </joint>
  <link name="RL_wheel">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0013575 -0.022348 0.022244"/>
      <mass value="0.86014"/>
      <inertia ixx="0.0044582" ixy="4.1515E-05" ixz="-9.6516E-05" iyy="0.0048298" iyz="0.00056141" izz="0.001609"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://vita00w/meshes/link_front_left_wheel.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.003 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.07"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_wheel_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0 0.0499 -0.21"/>
    <parent link="RL_calf"/>
    <child link="RL_wheel"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" velocity="37.7"/>
  </joint>
</robot>