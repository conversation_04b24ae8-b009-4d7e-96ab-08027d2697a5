<mujoco model="vita00">
  <compiler angle="radian" meshdir="meshes/"/>

  <option cone="elliptic" impratio="100" />

  <default>
    <default class="vita">
      <geom friction="0.4" margin="0.001" condim="1"/>
      <joint axis="0 1 0" damping="0.1" armature="0.01" frictionloss="0.2"/>
      <motor ctrlrange="-23.7 23.7"/>
      <default class="abduction">
        <joint axis="1 0 0" range="-0.6632 0.6632" actuatorfrcrange="-17 17"/>
      </default>
      <default class="hip">
        <default class="front_hip">
          <joint range="-0.87266 3.14159" actuatorfrcrange="-17 17"/>
        </default>
        <default class="back_hip">
          <joint range="-0.87266 4.1713" actuatorfrcrange="-17 17"/>
        </default>
      </default>
      <default class="knee">
        <joint range="-2.7925 -0.5236" actuatorfrcrange="-29.4 29.4"/>
      </default>
      <default class="visual">
        <geom type="mesh" contype="0" conaffinity="0" group="2" density="0" rgba="0.752941 0.752941 0.752941 1"/>
      </default>
      <default class="collision">
        <geom group="3"/>
        <default class="foot">
          <geom size="0.022" pos="-0.002 0 -0.22"/>
        </default>
      </default>
    </default>
  </default>

  <extension>
    <plugin plugin="mujoco.pid">
      <instance name="pid_controller">
        <config key="kp" value="30.0"/>  <!-- 位置比例增益 -->
        <config key="ki" value="5.0"/>   <!-- 积分增益，提供"记忆"功能 -->
        <config key="kd" value="1.0"/>   <!-- 微分增益，提供阻尼 -->
        <config key="imax" value="10"/>  <!-- 积分项最大值 -->
      </instance>
    </plugin>
  </extension>

  <asset>
    <mesh name="trunk" file="trunk.STL"/>
    <mesh name="Gemini_335" file="Gemini_335.STL"/>
    <mesh name="Livox_mid360" file="Livox_mid360.STL"/>
    <mesh name="orinNX" file="orinNX.STL"/>
    <mesh name="hip" file="hip.STL"/>
    <mesh name="thigh_left" file="thigh_left.STL"/>
    <mesh name="calf" file="calf.STL"/>
    <mesh name="thigh_right" file="thigh_right.STL"/>
  </asset>

  <worldbody>
    <body name="link_trunk" pos="0 0 0.455" childclass="vita">
      <inertial pos="-0.00775 0 0" quat="-0.0101731 0.714893 -0.00203209 0.699157" mass="7.119" diaginertia="0.100569 0.0861386 0.0287645"/>
      <freejoint name="joint_fixed_world"/>
      <geom type="mesh" class="visual" mesh="trunk"/>
      <geom pos="0.29866 0 0.0215" quat="0.500003 0.5 0.499997 0.5" type="mesh" class="visual" mesh="Gemini_335"/>
      <geom pos="0.22454 0 0.07165" quat="-0.0922959 0.0922965 0.701055 0.70106" type="mesh" class="visual" mesh="Livox_mid360"/>
      <geom pos="0 0 0.075" quat="1 0 0 0" type="mesh" class="visual" mesh="orinNX"/>
      <geom size="0.16 0.09 0.08" type="box" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
      <site name="trunk_imu" pos="0.05875 0.06195 -0.055"/>
      
      <body name="link_front_left_hip" pos="0.206 0.05 0">
        <inertial pos="-0.00249 0.00198 1.8e-05" quat="0.475388 0.475388 0.523456 0.523456" mass="0.506" diaginertia="0.000294462 0.000258577 0.000161487"/>
        <joint name="joint_front_left_abad" class="abduction"/>
        <geom type="mesh" class="visual" mesh="hip"/>
        <geom size="0.048 0.02" pos="0 0.08 0" quat="0.707105 0.707108 0 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
        <body name="link_front_left_thigh" pos="0 0.0893 0">
          <inertial pos="-0.00615 -0.01668 -0.0405" quat="0.412194 -0.0627541 0.0427849 0.907925" mass="1.094" diaginertia="0.00637756 0.00604806 0.000674383"/>
          <joint name="joint_front_left_thigh_pitch" class="front_hip"/>
          <geom type="mesh" class="visual" mesh="thigh_left"/>
          <geom size="0.1 0.012 0.0165" pos="-0.0225 0 -0.12" quat="0.707105 0 0.707108 0" type="box" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
          <body name="link_front_left_calf" pos="0 0 -0.22">
            <inertial pos="0.00163811 0 -0.14062" quat="0.707009 0.0117686 0.0117686 0.707009" mass="0.185" diaginertia="0.00149661 0.0014766 4.2176e-05"/>
            <joint name="joint_front_left_calf_pitch" class="knee"/>
            <geom type="mesh" class="visual" mesh="calf"/>
            <geom size="0.012 0.06" pos="0 0 -0.06" quat="0.993956 0 -0.109778 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom size="0.011 0.0325" pos="0.018 0 -0.16" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom name="FL" class="foot"/>
            <body name="link_front_left_foot" pos="-0.002 0 -0.22"/>
          </body>
        </body>
      </body>
      
      <body name="link_front_right_hip" pos="0.206 -0.05 0">
        <inertial pos="-0.00249 -0.00198 1.8e-05" quat="0.523456 0.523456 0.475388 0.475388" mass="0.506" diaginertia="0.000294462 0.000258577 0.000161487"/>
        <joint name="joint_front_right_abad" class="abduction"/>
        <geom quat="0 1 0 0" type="mesh" class="visual" mesh="hip"/>
        <geom size="0.048 0.02" pos="0 -0.08 0" quat="0.707105 0.707108 0 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
        <body name="link_front_right_thigh" pos="0 -0.0893 0">
          <inertial pos="-0.00615 0.01668 -0.0405" quat="0.907925 0.0427849 -0.0627541 0.412194" mass="1.094" diaginertia="0.00637756 0.00604806 0.000674383"/>
          <joint name="joint_front_right_thigh_pitch" class="front_hip"/>
          <geom type="mesh" class="visual" mesh="thigh_right"/>
          <geom size="0.1 0.012 0.0165" pos="-0.0225 0 -0.12" quat="0.707105 0 0.707108 0" type="box" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
          <body name="link_front_right_calf" pos="0 0 -0.22">
            <inertial pos="0.00163811 0 -0.14062" quat="0.707009 0.0117686 0.0117686 0.707009" mass="0.185" diaginertia="0.00149661 0.0014766 4.2176e-05"/>
            <joint name="joint_front_right_calf_pitch" class="knee"/>
            <geom type="mesh" class="visual" mesh="calf"/>
            <geom size="0.012 0.06" pos="0 0 -0.06" quat="0.993956 0 -0.109778 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom size="0.011 0.0325" pos="0.018 0 -0.16" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom name="FR" class="foot"/>
            <body name="link_front_right_foot" pos="-0.002 0 -0.22"/>
          </body>
        </body>
      </body>
      
      <body name="link_rear_left_hip" pos="-0.206 0.05 0">
        <inertial pos="0.00249 0.00198 1.8e-05" quat="0.523456 0.523456 0.475388 0.475388" mass="0.506" diaginertia="0.000294462 0.000258577 0.000161487"/>
        <joint name="joint_rear_left_abad" class="abduction"/>
        <geom quat="0 0 1 0" type="mesh" class="visual" mesh="hip"/>
        <geom size="0.048 0.02" pos="0 0.08 0" quat="0.707105 0.707108 0 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
        <body name="link_rear_left_thigh" pos="0 0.0893 0">
          <inertial pos="0.00615 -0.01668 -0.0405" quat="0.907925 -0.0427849 0.0627541 0.412194" mass="1.094" diaginertia="0.00637756 0.00604806 0.000674383"/>
          <joint name="joint_rear_left_thigh_pitch" class="back_hip"/>
          <geom type="mesh" class="visual" mesh="thigh_left"/>
          <geom size="0.1 0.012 0.0165" pos="-0.0225 0 -0.12" quat="0.707105 0 0.707108 0" type="box" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
          <body name="link_rear_left_calf" pos="0 0 -0.22">
            <inertial pos="0.00163811 0 -0.14062" quat="0.707009 0.0117686 0.0117686 0.707009" mass="0.185" diaginertia="0.00149661 0.0014766 4.2176e-05"/>
            <joint name="joint_rear_left_calf_pitch" class="knee"/>
            <geom type="mesh" class="visual" mesh="calf"/>
            <geom size="0.012 0.06" pos="0 0 -0.06" quat="0.993956 0 -0.109778 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom size="0.011 0.0325" pos="0.018 0 -0.16" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom name="RL" class="foot"/>
            <body name="link_rear_left_foot" pos="-0.002 0 -0.22"/>
          </body>
        </body>
      </body>
      
      <body name="link_rear_right_hip" pos="-0.206 -0.05 0">
        <inertial pos="0.00249 -0.00198 1.8e-05" quat="0.475388 0.475388 0.523456 0.523456" mass="0.506" diaginertia="0.000294462 0.000258577 0.000161487"/>
        <joint name="joint_rear_right_abad" class="abduction"/>
        <geom quat="0 0 0 -1" type="mesh" class="visual" mesh="hip"/>
        <geom size="0.048 0.02" pos="0 -0.08 0" quat="0.707105 0.707108 0 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
        <body name="link_rear_right_thigh" pos="0 -0.0893 0">
          <inertial pos="0.00615 0.01668 -0.0405" quat="0.412194 0.0627541 -0.0427849 0.907925" mass="1.094" diaginertia="0.00637756 0.00604806 0.000674383"/>
          <joint name="joint_rear_right_thigh_pitch" class="back_hip"/>
          <geom type="mesh" class="visual" mesh="thigh_right"/>
          <geom size="0.1 0.012 0.0165" pos="-0.0225 0 -0.12" quat="0.707105 0 0.707108 0" type="box" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
          <body name="link_rear_right_calf" pos="0 0 -0.22">
            <inertial pos="0.00163811 0 -0.14062" quat="0.707009 0.0117686 0.0117686 0.707009" mass="0.185" diaginertia="0.00149661 0.0014766 4.2176e-05"/>
            <joint name="joint_rear_right_calf_pitch" class="knee"/>
            <geom type="mesh" class="visual" mesh="calf"/>
            <geom size="0.012 0.06" pos="0 0 -0.06" quat="0.993956 0 -0.109778 0" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom size="0.011 0.0325" pos="0.018 0 -0.16" type="cylinder" class="collision" rgba="0.752941 0.752941 0.752941 1"/>
            <geom name="RR" class="foot"/>
            <body name="link_rear_right_foot" pos="-0.002 0 -0.22"/>
          </body>
        </body>
      </body>
    </body>
  </worldbody>

  <actuator>
    <!-- FR - 前右 -->
    <!-- <motor name="joint_front_right_abad" joint="joint_front_right_abad"/>
    <motor name="joint_front_right_thigh_pitch" joint="joint_front_right_thigh_pitch"/>
    <motor name="joint_front_right_calf_pitch" joint="joint_front_right_calf_pitch"/> -->
    <plugin name="joint_front_right_abad" joint="joint_front_right_abad" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_front_right_thigh_pitch" joint="joint_front_right_thigh_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_front_right_calf_pitch" joint="joint_front_right_calf_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    
    <!-- FL - 前左 -->
    <plugin name="joint_front_left_abad" joint="joint_front_left_abad" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_front_left_thigh_pitch" joint="joint_front_left_thigh_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_front_left_calf_pitch" joint="joint_front_left_calf_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <!-- RR - 后右 -->
    <plugin name="joint_rear_right_abad" joint="joint_rear_right_abad" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_rear_right_thigh_pitch" joint="joint_rear_right_thigh_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_rear_right_calf_pitch" joint="joint_rear_right_calf_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <!-- RL - 后左 -->
    <plugin name="joint_rear_left_abad" joint="joint_rear_left_abad" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_rear_left_thigh_pitch" joint="joint_rear_left_thigh_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
    <plugin name="joint_rear_left_calf_pitch" joint="joint_rear_left_calf_pitch" plugin="mujoco.pid" instance="pid_controller" actdim="1"/>
  </actuator>

  <sensor>
    <!-- 关节位置传感器 - 按照与执行器相同的顺序排列 -->
    <!-- FR - 前右 -->
    <jointpos name="joint_front_right_abad_pos" joint="joint_front_right_abad"/>
    <jointpos name="joint_front_right_thigh_pitch_pos" joint="joint_front_right_thigh_pitch"/>
    <jointpos name="joint_front_right_calf_pitch_pos" joint="joint_front_right_calf_pitch"/>
    <!-- FL - 前左 -->
    <jointpos name="joint_front_left_abad_pos" joint="joint_front_left_abad"/>
    <jointpos name="joint_front_left_thigh_pitch_pos" joint="joint_front_left_thigh_pitch"/>
    <jointpos name="joint_front_left_calf_pitch_pos" joint="joint_front_left_calf_pitch"/>
    <!-- RR - 后右 -->
    <jointpos name="joint_rear_right_abad_pos" joint="joint_rear_right_abad"/>
    <jointpos name="joint_rear_right_thigh_pitch_pos" joint="joint_rear_right_thigh_pitch"/>
    <jointpos name="joint_rear_right_calf_pitch_pos" joint="joint_rear_right_calf_pitch"/>
    <!-- RL - 后左 -->
    <jointpos name="joint_rear_left_abad_pos" joint="joint_rear_left_abad"/>
    <jointpos name="joint_rear_left_thigh_pitch_pos" joint="joint_rear_left_thigh_pitch"/>
    <jointpos name="joint_rear_left_calf_pitch_pos" joint="joint_rear_left_calf_pitch"/>

    <!-- 关节速度传感器 - 按照与执行器相同的顺序排列 -->
    <!-- FR - 前右 -->
    <jointvel name="joint_front_right_abad_vel" joint="joint_front_right_abad"/>
    <jointvel name="joint_front_right_thigh_pitch_vel" joint="joint_front_right_thigh_pitch"/>
    <jointvel name="joint_front_right_calf_pitch_vel" joint="joint_front_right_calf_pitch"/>
    <!-- FL - 前左 -->
    <jointvel name="joint_front_left_abad_vel" joint="joint_front_left_abad"/>
    <jointvel name="joint_front_left_thigh_pitch_vel" joint="joint_front_left_thigh_pitch"/>
    <jointvel name="joint_front_left_calf_pitch_vel" joint="joint_front_left_calf_pitch"/>
    <!-- RR - 后右 -->
    <jointvel name="joint_rear_right_abad_vel" joint="joint_rear_right_abad"/>
    <jointvel name="joint_rear_right_thigh_pitch_vel" joint="joint_rear_right_thigh_pitch"/>
    <jointvel name="joint_rear_right_calf_pitch_vel" joint="joint_rear_right_calf_pitch"/>
    <!-- RL - 后左 -->
    <jointvel name="joint_rear_left_abad_vel" joint="joint_rear_left_abad"/>
    <jointvel name="joint_rear_left_thigh_pitch_vel" joint="joint_rear_left_thigh_pitch"/>
    <jointvel name="joint_rear_left_calf_pitch_vel" joint="joint_rear_left_calf_pitch"/>

    <!-- 关节力矩传感器 - 按照与执行器相同的顺序排列 -->
    <!-- FR - 前右 -->
    <jointactuatorfrc name="joint_front_right_abad_torque" joint="joint_front_right_abad" noise="0.01"/>
    <jointactuatorfrc name="joint_front_right_thigh_pitch_torque" joint="joint_front_right_thigh_pitch" noise="0.01"/>
    <jointactuatorfrc name="joint_front_right_calf_pitch_torque" joint="joint_front_right_calf_pitch" noise="0.01"/>
    <!-- FL - 前左 -->
    <jointactuatorfrc name="joint_front_left_abad_torque" joint="joint_front_left_abad" noise="0.01"/>
    <jointactuatorfrc name="joint_front_left_thigh_pitch_torque" joint="joint_front_left_thigh_pitch" noise="0.01"/>
    <jointactuatorfrc name="joint_front_left_calf_pitch_torque" joint="joint_front_left_calf_pitch" noise="0.01"/>
    <!-- RR - 后右 -->
    <jointactuatorfrc name="joint_rear_right_abad_torque" joint="joint_rear_right_abad" noise="0.01"/>
    <jointactuatorfrc name="joint_rear_right_thigh_pitch_torque" joint="joint_rear_right_thigh_pitch" noise="0.01"/>
    <jointactuatorfrc name="joint_rear_right_calf_pitch_torque" joint="joint_rear_right_calf_pitch" noise="0.01"/>
    <!-- RL - 后左 -->
    <jointactuatorfrc name="joint_rear_left_abad_torque" joint="joint_rear_left_abad" noise="0.01"/>
    <jointactuatorfrc name="joint_rear_left_thigh_pitch_torque" joint="joint_rear_left_thigh_pitch" noise="0.01"/>
    <jointactuatorfrc name="joint_rear_left_calf_pitch_torque" joint="joint_rear_left_calf_pitch" noise="0.01"/>

    <!-- IMU传感器 -->
    <framequat name="trunk_quat" objtype="site" objname="trunk_imu"/>
    <gyro name="trunk_gyro" site="trunk_imu"/>
    <accelerometer name="trunk_acc" site="trunk_imu"/>
    
    <!-- 位置和速度传感器 -->
    <framepos name="trunk_pos" objtype="site" objname="trunk_imu"/>
    <framelinvel name="trunk_vel" objtype="site" objname="trunk_imu"/>
  </sensor>

  <keyframe>
    <key name="lie_down" qpos="0 0 0.15 1 0 0 0 -0.2 1.3 -2.8 0.2 1.3 -2.8 -0.2 1.3 -2.8 0.2 1.3 -2.8" 
         ctrl="-0.2 1.3 -2.8 0.2 1.3 -2.8 -0.2 1.3 -2.8 0.2 1.3 -2.8" />
    <key name="fix_stand" qpos="0 0 0.32 1 0 0 0 0.1 0.8 -1.5 -0.1 0.8 -1.5 0.1 1.0 -1.5 -0.1 1.0 -1.5" 
         ctrl="0.1 0.8 -1.5 -0.1 0.8 -1.5 0.1 1.0 -1.5 -0.1 1.0 -1.5" />
  </keyframe>
</mujoco>
