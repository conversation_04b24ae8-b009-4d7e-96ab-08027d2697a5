<?xml version="1.0" encoding="utf-8" ?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>)
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot name="vita01b_c">
  <mujoco>
    <compiler
            balanceinertia="true"
            discardvisual="false"
            meshdir="../meshes/"
        />
  </mujoco>
  <link name="base">
    <inertial>
      <origin xyz="0.029290 0.001715 0.012137" rpy="0 0 0" />
      <mass value="9.642602" />
      <inertia
                ixx="0.070078"
                ixy="0.001414"
                ixz="-0.027396"
                iyy="0.144116"
                iyz="0.000981"
                izz="0.124239"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/base_link.STL" />
      </geometry>
      <material name="">
        <color rgba="0.75294 0.75294 0.75294 1" />
      </material>
    </visual>
    <collision name='collision_middle_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="0.0 0.0 0.0" />
      <geometry>
        <box size="0.266658 0.165694 0.113034" />
      </geometry>
    </collision>
    <collision name='collision_head_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="0.167365 -0.000436 0.16761" />
      <geometry>
        <box size="0.169204 0.090048 0.083746" />
      </geometry>
    </collision>
    <collision name='collision_front_dangban_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="0.247938 0.0 0.006492" />
      <geometry>
        <box size="0.011651 0.058022 0.058022" />
      </geometry>
    </collision>
    <collision name='collision_back_dangban_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="-0.249278 0.0 0.006492" />
      <geometry>
        <box size="0.011651 0.058022 0.047636" />
      </geometry>
    </collision>
    <collision name='collision_low_bar_FL_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="0.115 0.0375 -0.081" />
      <geometry>
        <box size="0.016 0.016 0.013" />
      </geometry>
    </collision>
    <collision name='collision_low_bar_FR_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="0.115 -0.0375 -0.081" />
      <geometry>
        <box size="0.016 0.016 0.013" />
      </geometry>
    </collision>
    <collision name='collision_low_bar_RL_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="-0.095 0.0375 -0.081" />
      <geometry>
        <box size="0.016 0.016 0.013" />
      </geometry>
    </collision>
    <collision name='collision_low_bar_RR_box'>
      <origin rpy="0.0 -0.0 0.0" xyz="-0.095 -0.0375 -0.081" />
      <geometry>
        <box size="0.016 0.016 0.013" />
      </geometry>
    </collision>
  </link>
  <link name="FR_hip">
    <inertial>
      <origin xyz="-0.030332 0.013811 0.000000" rpy="0 0 0" />
      <mass value="0.051654" />
      <inertia
                ixx="0.000009"
                ixy="-0.000005"
                ixz="0.000000"
                iyy="0.000033"
                iyz="0.000000"
                izz="0.000032"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FR_hip.STL" />
      </geometry>
      <material name="">
        <color rgba="0.89804 0.91765 0.92941 1" />
      </material>
    </visual>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin xyz="0.1875 -0.051 0" rpy="0 0 0" />
    <parent link="base" />
    <child link="FR_hip" />
    <axis xyz="1 0 0" />
    <limit lower="-0.732" upper="0.732" effort="17" velocity="37.7" />
  </joint>
  <link name="FR_thigh">
    <inertial>
      <origin xyz="-0.011154 0.038429 -0.020224" rpy="0 0 0" />
      <mass value="1.529007" />
      <inertia
                ixx="0.005791"
                ixy="-0.000649"
                ixz="0.000136"
                iyy="0.010295"
                iyz="-0.001221"
                izz="0.008663"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FR_thigh.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_mainlink_box'>
      <origin rpy="0.0 -0.150338 0.0" xyz="-0.026068 -0.000724 -0.10287" />
      <geometry>
        <box size="0.025763 0.02 0.1" />
      </geometry>
    </collision>
    <collision name='collision_self_check_box'>
      <origin rpy="0.0 0.474394 0.0" xyz="0.024156 -0.001579 -0.05165" />
      <geometry>
        <box size="0.001751 0.020706 0.05066" />
      </geometry>
    </collision>
    <collision name='collision_motor_cylinder'>
      <origin rpy="4.712389 -0.0 0.0" xyz="0.0 0.02351 -0.000177" />
      <geometry>
        <cylinder length="0.07691" radius="0.044" />
      </geometry>
    </collision>
    <collision name='collision_revo_left_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 0.014268 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
    <collision name='collision_revo_right_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 -0.01304 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin xyz="0 -0.096 0" rpy="0 0 0" />
    <parent link="FR_hip" />
    <child link="FR_thigh" />
    <axis xyz="0 1 0" />
    <limit lower="-1.657" upper="3.227" effort="17" velocity="37.7" />
  </joint>
  <link name="FR_calf">
    <inertial>
      <origin xyz="0.004528 0.000000 -0.112403" rpy="0 0 0" />
      <mass value="0.130624" />
      <inertia
                ixx="0.000815"
                ixy="0.000000"
                ixz="0.000055"
                iyy="0.000827"
                iyz="0.000000"
                izz="0.000027"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FR_calf.STL" />
      </geometry>
      <material name="">
        <color rgba="0.10196 0.10196 0.10196 1" />
      </material>
    </visual>
    <collision name='collision_upper_box'>
        <origin rpy="0.0 5.40213 0.0" xyz="0.018049 0.0 -0.186508"/>
        <geometry>
          <box size="0.024 0.015288 0.048535"/>
        </geometry>
      </collision>
    <collision name='collision_middle_box'>
        <origin rpy="0.0 -11.818827 0.0" xyz="0.005529 0.0 -0.099894"/>
        <geometry>
          <box size="0.02 0.024 0.12"/>
        </geometry>
      </collision>
    <collision name='collision_lower_box'>
        <origin rpy="0.0 0.757596 0.0" xyz="-0.005451 -0.0 -0.017378"/>
        <geometry>
          <box size="0.028 0.015288 0.04"/>
        </geometry>
      </collision>
    <collision name='collision_backprotect_box'>
        <origin rpy="0.0 21.914913 0.0" xyz="-0.005731 0.0 -0.175631"/>
        <geometry>
          <box size="0.004 0.015288 0.052"/>
        </geometry>
      </collision>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin xyz="0 0 -0.1985" rpy="0 0 0" />
    <parent link="FR_thigh" />
    <child link="FR_calf" />
    <axis xyz="0 1 0" />
    <limit lower="-2.69" upper="-0.822" effort="34" velocity="18.9" />
  </joint>
  <link name="FR_foot">
    <inertial>
      <origin xyz="-0.004285 0.000000 -0.005134" rpy="0 0 0" />
      <mass value=" 0.035884" />
      <inertia
                ixx="0.000008"
                ixy="0.000000"
                ixz="0.000001"
                iyy="0.000006"
                iyz="0.000000"
                izz="0.000008"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FR_foot.STL" />
      </geometry>
      <material name="">
        <color rgba="0.101960784313725 0.101960784313725 0.101960784313725 1" />
      </material>
    </visual>
    <collision name='collision_foot_sphere'>
      <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0" />
      <geometry>
        <sphere radius="0.022" />
      </geometry>
    </collision>
  </link>
  <joint name="FR_foot_joint" type="fixed" dont_collapse="true">
    <origin xyz="0 0 -0.21303" rpy="0 0 0" />
    <parent link="FR_calf" />
    <child link="FR_foot" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="FL_hip">
    <inertial>
      <origin xyz="-0.030332 -0.013811 0.000000" rpy="0 0 0" />
      <mass value="0.051654" />
      <inertia
                ixx="0.000009"
                ixy="0.000005"
                ixz="0.000000"
                iyy="0.000033"
                iyz="0.000000"
                izz="0.000032"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FL_hip.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin xyz="0.1875 0.051 0" rpy="0 0 0" />
    <parent link="base" />
    <child link="FL_hip" />
    <axis xyz="1 0 0" />
    <limit lower="-0.732" upper="0.732" effort="17" velocity="37.7" />
  </joint>
  <link name="FL_thigh">
    <inertial>
      <origin xyz="-0.011154 -0.038429 -0.020224" rpy="0 0 0" />
      <mass value="1.529007" />
      <inertia
                ixx="0.005791"
                ixy="0.000649"
                ixz="0.000136"
                iyy="0.010295"
                iyz="0.001221"
                izz="0.008663"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FL_thigh.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_mainlink_box'>
      <origin rpy="0.0 -0.150338 0.0" xyz="-0.026068 0.000724 -0.10287" />
      <geometry>
        <box size="0.025763 0.02 0.1" />
      </geometry>
    </collision>
    <collision name='collision_self_check_box'>
      <origin rpy="0.0 0.474394 0.0" xyz="0.024156 0.001579 -0.05165" />
      <geometry>
        <box size="0.001751 0.020706 0.05066" />
      </geometry>
    </collision>
    <collision name='collision_motor_cylinder'>
      <origin rpy="1.570796 -0.0 0.0" xyz="0.0 -0.02351 -0.000177" />
      <geometry>
        <cylinder length="0.07691" radius="0.044" />
      </geometry>
    </collision>
    <collision name='collision_revo_left_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 -0.014268 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
    <collision name='collision_revo_right_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 0.01304 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin xyz="0 0.096 0" rpy="0 0 0" />
    <parent link="FL_hip" />
    <child link="FL_thigh" />
    <axis xyz="0 1 0" />
    <limit lower="-1.657" upper="3.227" effort="17" velocity="37.7" />
  </joint>
  <link name="FL_calf">
    <inertial>
      <origin xyz="0.004528 0.000000 -0.112403" rpy="0 0 0" />
      <mass value="0.130624" />
      <inertia
                ixx="0.000815"
                ixy="0.000000"
                ixz="0.000055"
                iyy="0.000827"
                iyz="0.000000"
                izz="0.000027"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FL_calf.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_upper_box'>
        <origin rpy="0.0 5.40213 0.0" xyz="0.018049 0.0 -0.186508"/>
        <geometry>
          <box size="0.024 0.015288 0.048535"/>
        </geometry>
      </collision>
    <collision name='collision_middle_box'>
        <origin rpy="0.0 -11.818827 0.0" xyz="0.005529 0.0 -0.099894"/>
        <geometry>
          <box size="0.02 0.024 0.12"/>
        </geometry>
      </collision>
    <collision name='collision_lower_box'>
        <origin rpy="0.0 0.757596 0.0" xyz="-0.005451 -0.0 -0.017378"/>
        <geometry>
          <box size="0.028 0.015288 0.04"/>
        </geometry>
      </collision>
    <collision name='collision_backprotect_box'>
        <origin rpy="0.0 21.914913 0.0" xyz="-0.005731 0.0 -0.175631"/>
        <geometry>
          <box size="0.004 0.015288 0.052"/>
        </geometry>
      </collision>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin xyz="0 0 -0.1985" rpy="0 0 0" />
    <parent link="FL_thigh" />
    <child link="FL_calf" />
    <axis xyz="0 1 0" />
    <limit lower="-2.69" upper="-0.822" effort="34" velocity="18.9" />
  </joint>
  <link name="FL_foot">
    <inertial>
      <origin xyz="-0.004285 0.000000 -0.005134" rpy="0 0 0" />
      <mass value=" 0.035884" />
      <inertia
                ixx="0.000008"
                ixy="0.000000"
                ixz="0.000001"
                iyy="0.000006"
                iyz="0.000000"
                izz="0.000008"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/FL_foot.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_foot_sphere'>
      <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0" />
      <geometry>
        <sphere radius="0.022" />
      </geometry>
    </collision>
  </link>
  <joint name="FL_foot_joint" type="fixed" dont_collapse="true">
    <origin xyz="0 0 -0.21303" rpy="0 0 0" />
    <parent link="FL_calf" />
    <child link="FL_foot" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="RR_hip">
    <inertial>
      <origin xyz="0.030332 0.013811 0.000000" rpy="0 0 0" />
      <mass value="0.051654" />
      <inertia
                ixx="0.000009"
                ixy="0.000005"
                ixz="0.000000"
                iyy="0.000033"
                iyz="0.000000"
                izz="0.000032"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RR_hip.STL" />
      </geometry>
      <material name="">
        <color rgba="0.898039215686275 0.917647058823529 0.929411764705882 1" />
      </material>
    </visual>
  </link>
  <joint name="RR_hip_joint" type="revolute">
    <origin xyz="-0.1875 -0.051 0" rpy="0 0 0" />
    <parent link="base" />
    <child link="RR_hip" />
    <axis xyz="1 0 0" />
    <limit lower="-0.732" upper="0.732" effort="17" velocity="37.7" />
  </joint>
  <link name="RR_thigh">
    <inertial>
      <origin xyz="-0.011154 0.038429 -0.020224" rpy="0 0 0" />
      <mass value="1.529007" />
      <inertia
                ixx="0.005791"
                ixy="-0.000649"
                ixz="0.000136"
                iyy="0.010295"
                iyz="-0.001221"
                izz="0.008663"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RR_thigh.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_mainlink_box'>
      <origin rpy="0.0 -0.150338 0.0" xyz="-0.026068 -0.000724 -0.10287" />
      <geometry>
        <box size="0.025763 0.02 0.1" />
      </geometry>
    </collision>
    <collision name='collision_self_check_box'>
      <origin rpy="0.0 0.474394 0.0" xyz="0.024156 -0.001579 -0.05165" />
      <geometry>
        <box size="0.001751 0.020706 0.05066" />
      </geometry>
    </collision>
    <collision name='collision_motor_cylinder'>
      <origin rpy="4.712389 -0.0 0.0" xyz="0.0 0.02351 -0.000177" />
      <geometry>
        <cylinder length="0.07691" radius="0.044" />
      </geometry>
    </collision>
    <collision name='collision_revo_left_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 0.014268 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
    <collision name='collision_revo_right_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 -0.01304 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin xyz="0 -0.096 0" rpy="0 0 0" />
    <parent link="RR_hip" />
    <child link="RR_thigh" />
    <axis xyz="0 1 0" />
    <limit lower="-0.61" upper="4.274" effort="17" velocity="37.7" />
  </joint>
  <link name="RR_calf">
    <inertial>
      <origin xyz="0.004528 0.000000 -0.112403" rpy="0 0 0" />
      <mass value="0.130624" />
      <inertia
                ixx="0.000815"
                ixy="0.000000"
                ixz="0.000055"
                iyy="0.000827"
                iyz="0.000000"
                izz="0.000027"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RR_calf.STL" />
      </geometry>
      <material name="">
        <color rgba="0.101960784313725 0.101960784313725 0.101960784313725 1" />
      </material>
    </visual>
    <collision name='collision_upper_box'>
        <origin rpy="0.0 5.40213 0.0" xyz="0.018049 0.0 -0.186508"/>
        <geometry>
          <box size="0.024 0.015288 0.048535"/>
        </geometry>
      </collision>
    <collision name='collision_middle_box'>
        <origin rpy="0.0 -11.818827 0.0" xyz="0.005529 0.0 -0.099894"/>
        <geometry>
          <box size="0.02 0.024 0.12"/>
        </geometry>
      </collision>
    <collision name='collision_lower_box'>
        <origin rpy="0.0 0.757596 0.0" xyz="-0.005451 -0.0 -0.017378"/>
        <geometry>
          <box size="0.028 0.015288 0.04"/>
        </geometry>
      </collision>
    <collision name='collision_backprotect_box'>
        <origin rpy="0.0 21.914913 0.0" xyz="-0.005731 0.0 -0.175631"/>
        <geometry>
          <box size="0.004 0.015288 0.052"/>
        </geometry>
      </collision>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin xyz="0 0 -0.1985" rpy="0 0 0" />
    <parent link="RR_thigh" />
    <child link="RR_calf" />
    <axis xyz="0 1 0" />
    <limit lower="-2.69" upper="-0.822" effort="34" velocity="18.9" />
  </joint>
  <link name="RR_foot">
    <inertial>
      <origin xyz="-0.004285 0.000000 -0.005134" rpy="0 0 0" />
      <mass value=" 0.035884" />
      <inertia
                ixx="0.000008"
                ixy="0.000000"
                ixz="0.000001"
                iyy="0.000006"
                iyz="0.000000"
                izz="0.000008"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RR_foot.STL" />
      </geometry>
      <material name="">
        <color rgba="0.101960784313725 0.101960784313725 0.101960784313725 1" />
      </material>
    </visual>
    <collision name='collision_foot_sphere'>
      <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0" />
      <geometry>
        <sphere radius="0.022" />
      </geometry>
    </collision>
  </link>
  <joint name="RR_foot_joint" type="fixed" dont_collapse="true">
    <origin xyz="0 0 -0.21303" rpy="0 0 0" />
    <parent link="RR_calf" />
    <child link="RR_foot" />
    <axis xyz="0 0 0" />
  </joint>
  <link name="RL_hip">
    <inertial>
      <origin xyz="0.030332 -0.013811 0.000000" rpy="0 0 0" />
      <mass value="0.051654" />
      <inertia
                ixx="0.000009"
                ixy="-0.000005"
                ixz="0.000000"
                iyy="0.000033"
                iyz="0.000000"
                izz="0.000032"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RL_hip.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
  </link>
  <joint name="RL_hip_joint" type="revolute">
    <origin xyz="-0.1875 0.051 0" rpy="0 0 0" />
    <parent link="base" />
    <child link="RL_hip" />
    <axis xyz="1 0 0" />
    <limit lower="-0.732" upper="0.732" effort="17" velocity="37.7" />
  </joint>
  <link name="RL_thigh">
    <inertial>
      <origin xyz="-0.011154 -0.038429 -0.020224" rpy="0 0 0" />
      <mass value="1.529007" />
      <inertia
                ixx="0.005791"
                ixy="0.000649"
                ixz="0.000136"
                iyy="0.010295"
                iyz="0.001221"
                izz="0.008663"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RL_thigh.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_mainlink_box'>
      <origin rpy="0.0 -0.150338 0.0" xyz="-0.026068 0.000724 -0.10287" />
      <geometry>
        <box size="0.025763 0.02 0.1" />
      </geometry>
    </collision>
    <collision name='collision_self_check_box'>
      <origin rpy="0.0 0.474394 0.0" xyz="0.024156 0.001579 -0.05165" />
      <geometry>
        <box size="0.001751 0.020706 0.05066" />
      </geometry>
    </collision>
    <collision name='collision_motor_cylinder'>
      <origin rpy="1.570796 -0.0 0.0" xyz="0.0 -0.02351 -0.000177" />
      <geometry>
        <cylinder length="0.07691" radius="0.044" />
      </geometry>
    </collision>
    <collision name='collision_revo_left_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 -0.014268 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
    <collision name='collision_revo_right_box'>
      <origin rpy="0.0 -0.191986 0.0" xyz="-0.00614 0.01304 -0.188644" />
      <geometry>
        <box size="0.03 0.004 0.04" />
      </geometry>
    </collision>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin xyz="0 0.096 0" rpy="0 0 0" />
    <parent link="RL_hip" />
    <child link="RL_thigh" />
    <axis xyz="0 1 0" />
    <limit lower="-0.61" upper="4.274" effort="17" velocity="37.7" />
  </joint>
  <link name="RL_calf">
    <inertial>
      <origin xyz="0.004528 0.000000 -0.112403" rpy="0 0 0" />
      <mass value="0.130624" />
      <inertia
                ixx="0.000815"
                ixy="0.000000"
                ixz="0.000055"
                iyy="0.000827"
                iyz="0.000000"
                izz="0.000027"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RL_calf.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_upper_box'>
        <origin rpy="0.0 5.40213 0.0" xyz="0.018049 0.0 -0.186508"/>
        <geometry>
          <box size="0.024 0.015288 0.048535"/>
        </geometry>
      </collision>
    <collision name='collision_middle_box'>
        <origin rpy="0.0 -11.818827 0.0" xyz="0.005529 0.0 -0.099894"/>
        <geometry>
          <box size="0.02 0.024 0.12"/>
        </geometry>
      </collision>
    <collision name='collision_lower_box'>
        <origin rpy="0.0 0.757596 0.0" xyz="-0.005451 -0.0 -0.017378"/>
        <geometry>
          <box size="0.028 0.015288 0.04"/>
        </geometry>
      </collision>
    <collision name='collision_backprotect_box'>
        <origin rpy="0.0 21.914913 0.0" xyz="-0.005731 0.0 -0.175631"/>
        <geometry>
          <box size="0.004 0.015288 0.052"/>
        </geometry>
      </collision>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin xyz="0 0 -0.1985" rpy="0 0 0" />
    <parent link="RL_thigh" />
    <child link="RL_calf" />
    <axis xyz="0 1 0" />
    <limit lower="-2.69" upper="-0.822" effort="34" velocity="18.9" />
  </joint>
  <link name="RL_foot">
    <inertial>
      <origin xyz="-0.004285 0.000000 -0.005134" rpy="0 0 0" />
      <mass value=" 0.035884" />
      <inertia
                ixx="0.000008"
                ixy="0.000000"
                ixz="0.000001"
                iyy="0.000006"
                iyz="0.000000"
                izz="0.000008"
            />
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0" />
      <geometry>
        <mesh filename="package://vita01b_c/meshes/RL_foot.STL" />
      </geometry>
      <material name="">
        <color rgba="1 1 1 1" />
      </material>
    </visual>
    <collision name='collision_foot_sphere'>
      <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 0.0" />
      <geometry>
        <sphere radius="0.022" />
      </geometry>
    </collision>
  </link>
  <joint name="RL_foot_joint" type="fixed" dont_collapse="true">
    <origin xyz="0 0 -0.21303" rpy="0 0 0" />
    <parent link="RL_calf" />
    <child link="RL_foot" />
    <axis xyz="0 0 0" />
  </joint>
</robot>
