cmake_minimum_required(VERSION 3.8)
project(lowlevel_msg)

if(POLICY CMP0148)
  cmake_policy(SET CMP0148 OLD)
endif()

find_program(CCACHE_FOUND ccache)

if(CCACHE_FOUND)
  set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
  set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)
endif(CCACHE_FOUND)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(std_msgs REQUIRED)

file(GLOB_RECURSE MSG_FILES RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/msg *.msg)

set(ABSOLUTE_MSG_FILES "")

foreach(MSG_FILE ${MSG_FILES})
  list(APPEND ABSOLUTE_MSG_FILES "msg/${MSG_FILE}")
endforeach()

rosidl_generate_interfaces(${PROJECT_NAME}
  ${ABSOLUTE_MSG_FILES}
  DEPENDENCIES std_msgs
)

ament_package()
