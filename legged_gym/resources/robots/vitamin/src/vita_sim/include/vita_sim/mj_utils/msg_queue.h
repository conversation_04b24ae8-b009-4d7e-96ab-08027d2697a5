// Copyright 2025 VitaDynamics Limited

#pragma once
#include <chrono>
#include <condition_variable>
#include <deque>
#include <iostream>
#include <mutex>
#include <utility>

namespace vita_sim {
template <typename Msg> class MessageQueue {
public:
  MessageQueue(size_t capacity = 100) : capacity_(capacity), queue_(capacity) {}

  ~MessageQueue() = default;

  /**
   * Put Msg to the end of the queue.
   *
   * @param msg Msg to put to the queue.
   */
  void put(Msg &msg);

  /**
   * Get message from the head of the queue.
   * Blocks until at least one message is available in the queue, or until
   * timeout happens. If get() returns due to timeout, returns a nullptr.
   *
   * @param timeoutMillis How many ms to wait for message until timeout happens.
   *                      0 = wait indefinitely.
   */
  std::unique_ptr<Msg> get(int timeoutMillis = 0);

  /**
   * Get message from the head of the queue.
   * Returns an empty pointer if no message is available.
   */
  std::shared_ptr<Msg> tryGet();

  /**
   * Get the size of the queue
   */
  size_t size();

  // dedault set to 100
  size_t capacity_{100};

  // Queue for the Msgs
  std::deque<std::shared_ptr<Msg>> queue_;

  // Mutex to protect access to the queue
  std::mutex queueMutex_;

  // Condition variable to wait for when getting Msgs from the queue
  std::condition_variable queueCond_;

  // Mutex to protect access to response map
  std::mutex responseMapMutex_;
};

} // namespace vita_sim

#include "msg_queue.impl.h"
