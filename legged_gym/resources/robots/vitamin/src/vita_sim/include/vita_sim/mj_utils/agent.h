// Copyright 2025 VitaDynamics Limited

#pragma once

#include <GLFW/glfw3.h>
#include <mujoco/mjxmacro.h>
#include <mujoco/mujoco.h>

#include <atomic>
#include <climits>
#include <deque>
#include <string>
#include <string_view>
#include <vector>

#ifndef USE_UNITREE_GO
#include <lowlevel_msg/msg/low_cmd.hpp>
#include <lowlevel_msg/msg/low_state.hpp>
#else
#include <unitree_go/msg/low_cmd.hpp>
#include <unitree_go/msg/low_state.hpp>
#endif
#include <sensor_msgs/msg/joy.hpp>

#include "simulate/array_safety.h"
#include "vita_sim/config.h"
#include "vita_sim/mj_utils/msg_queue.h"

namespace vita_sim {

using LineQue = std::deque<std::tuple<mjtNum, float>>;

#ifndef USE_UNITREE_GO
using LowCmdMsg = lowlevel_msg::msg::LowCmd;
using LowStateMsg = lowlevel_msg::msg::LowState;
#else
using LowCmdMsg = unitree_go::msg::LowCmd;
using LowStateMsg = unitree_go::msg::LowState;
#endif

// namely hip, thigh, calf
static constexpr int kJointType = 3;

// number of foots
static constexpr int kFootNum = 4;

// color solution from https://colorbrewer2.org/
static constexpr float kFigLineColors[][3]{
    {230, 85, 13},   {117, 107, 177}, {127, 205, 187}, {253, 174, 107},
    {188, 189, 220}, {237, 248, 177}, {240, 59, 32}};

// ghost robot color in RGBA
static constexpr float kGhostColor[4]{99, 99, 99, 0.8};

// real time visualized robot color in RGBA
static constexpr float kRtVisColor[4]{255, 247, 188, 0.8};

// scale factor of the points rendered in the GUI
static constexpr float kPntScale = 0.8;
// store factor of points
static constexpr float kStorePntFactor = 10.0;

// state index of simulation sect in main ui
static constexpr int kSimSectIndex = 2;

// number of pos and quat of the base joint
static constexpr int kPos = 3;
static constexpr int kQuat = 4;

// max char buffer of geom label
static constexpr int kMaxChar = 100;

// joy stick axes size
static constexpr int kJoyAxes = 6;

struct UserGeom {
  std::shared_ptr<mjModel> model;
  std::shared_ptr<mjData> data;
  std::shared_ptr<mjvOption> opt;
  std::shared_ptr<mjvPerturb> pert;
  std::vector<mjtNum> hist_states{};
  int nq_;
  std::vector<float> color_setting;
  UserGeom(const std::string &modelpath, const float *color);
  void AddState2Hist(int index);
};

struct FigData {
  std::shared_ptr<mjvFigure> fig;
  std::vector<LineQue> line_data{};
  FigData(const std::string &name,
          const std::vector<std::string> &legend_names);
};

class Agent {
 public:
  Agent();
  ~Agent() = default;

  void InitAgentConfig(const SimConfig &sim_config, const double &pixel);

  // graphical user interface elements for agent and task
  void MakeGUI(mjUI &ui);

  void InitPlot();
  void InitUserGeom(const std::string &modelpath,
                    std::shared_ptr<UserGeom> &geom, const float *color);
  inline void InitGhost(const std::string &modelpath) {
    InitUserGeom(modelpath, ghost_geom_, kGhostColor);
  }
  inline void InitRtVis(const std::string &modelpath) {
    InitUserGeom(modelpath, rt_vis_geom_, kRtVisColor);
  }

  inline void SetFigPnt(double resol, float fig_range) {
    fig_range_ = fig_range;
    fig_pnt_ = fig_range_ * resol * kPntScale;
  }
  void ResetPlot();
  void Plots(const mjModel *mj_model, const mjData *mj_data, bool is_run);
  void PlotShow(mjrRect *rect, mjrContext *con);
  void PlotReset();
  void PlotResetData(mjvFigure &fig, int index, int length);
  void ShowDepth(mjvScene *scn, mjrContext *con, mjModel *m_, mjrRect *rect);
  void ShowCommandId(mjrRect *rect, mjrContext *con);

  void UpdateGeom(mjvScene *scn, std::shared_ptr<UserGeom> &geom);
  void UpdateGhostQpos(mjData *data);
  void UpdateRtVisQpos(mjData *data);

  inline void SaveCurSelectedBody(mjvPerturb *pert) {
    sel_body_ = pert->select;
  }
  /**
   * @brief apply force on selected body, should be added before mujoco step
   */
  void ApplyForceOnSelBody(mjModel *mj_m, mjData *mj_d);

  void ShowAppliedForce(mjvScene *scn, mjModel *mj_m, mjData *mj_d);

  /**
   * @brief reuse vis geoms implemented by mujoco, add ctrl value below the
   * label
   */
  void ShowActuatorCtrlValue(mjvScene *scn, mjModel *mj_m, mjData *mj_d);

  inline void UpdateLowCmd() {
    auto cur_cmd = low_cmd_que_->tryGet();
    if (!cur_cmd) return;
    cur_lowcmd_ = cur_cmd;
  }
  inline void UpdateRtLowState() {
    rt_low_state_ = rt_low_state_que_->tryGet();
  }
  inline void UpdateJoyStick() {
    auto cur_joy = joy_stick_que_->tryGet();
    if (!cur_joy) return;
    cur_joy_ = cur_joy;
  }
  std::shared_ptr<LowStateMsg> CurrentRtLowState() const {
    return rt_low_state_;
  }

  inline void SetHistBufSize(int states, int buf_size) {
    state_size_ = states;
    hist_buf_size_ = buf_size;
  }
  void InitHistStates(mjModel *mj_model, mjData *mj_data, mjuiSection *sect);
  void InitHistStatesUserGeom(std::shared_ptr<UserGeom> geom);

  void OnKeyRight(mjModel *mj_model, mjData *mj_Data, mjUI *ui, int sec_id,
                  mjuiState *ui_state, mjrContext *con);
  void OnKeyLeft(mjModel *mj_model, mjData *mj_Data, mjUI *ui, int sec_id,
                 mjuiState *ui_state, mjrContext *con);
  void AddState2Hist(mjModel *mj_model, mjData *mj_data);
  void LoadState(mjModel *mj_model, mjData *mj_data, mjDoubleVec &hist_buf);
  void LoadScrubState(mjModel *mj_model, mjData *mj_data);

  void LoadUserGeomState(mjvScene *scn, std::shared_ptr<UserGeom> geom);
  void ChangeRawHistoryState(mjUI *ui, int range);

  void SetSimBeginTime(mjData *mj_Data);

  bool CheckRTVisTopic(char *error_msg, size_t error_msg_size);

  std::unique_ptr<MessageQueue<LowCmdMsg>> low_cmd_que_{};
  std::unique_ptr<MessageQueue<LowStateMsg>> rt_low_state_que_{};
  std::unique_ptr<MessageQueue<sensor_msgs::msg::Joy>> joy_stick_que_{};

  int is_show_custom_figs_{0};
  int is_show_tau_figs_{0};
  int is_show_ssr_figs_{0};
  int shadow_mode_{0};
  int depth_cam_{0};
  int is_rt_vis_{0};
  int is_tau_est_{0};
  int is_show_dynamics_{0};

  // history scruber
  int scrub_index_{0};
  std::vector<mjtNum> hist_buf_;  // history buffer (nhistory x state_size)
  int state_size_ = 0;            // number of mjtNums in a history buffer state
  int nhistory_ = 0;              // number of states saved in history buffer
  int history_cursor_ = 0;        // cursor pointing at last saved state
  std::vector<mjtNum> history_;   // history buffer (nhistory x state_size)

  bool is_load_hist_{false};
  std::vector<uint32_t> cmd_history_;  // history buffer for command IDs

  std::shared_ptr<UserGeom> ghost_geom_;
  std::shared_ptr<UserGeom> rt_vis_geom_;

  // history buffer for state IDs
  std::vector<uint32_t> state_history_{};

 private:
  void PlotUpdateData(std::shared_ptr<FigData> fig_data,
                      const mjtNum &cur_time);
  vita_sim::SimConfig sim_config_;
  std::vector<std::shared_ptr<FigData>> joint_figs_{};
  std::vector<std::shared_ptr<FigData>> tau_figs_{};
  std::vector<std::shared_ptr<FigData>> tau_est_figs_{};
  std::vector<std::shared_ptr<FigData>> global_dynamics_figs_{};
  std::vector<std::shared_ptr<FigData>> mj_ssr_figs_{};
  std::shared_ptr<LowCmdMsg> cur_lowcmd_{};
  std::shared_ptr<LowStateMsg> rt_low_state_{};
  std::shared_ptr<sensor_msgs::msg::Joy> cur_joy_{};
  // amount of points rendered in figure in one frame
  float fig_pnt_;
  float fig_range_{1.0};
  // number of stored states
  int hist_states_{10'000};
  // amount of memory, default as 100MB
  int hist_buf_size_{100'000'000};
  // sim-mcap time diff
  std::atomic<bool> is_first_recv_cmd_{false};
  float first_cmd_time_{0.0};
  uint32_t last_cmd_id_{0};
  uint32_t last_state_id_{0};
  // weight that applied to selected body
  mjtNum load_weight_{0};
  int sel_body_{-1};
};

}  // namespace vita_sim
