robot: "vita01b"
robot_scene: "scene_locoindoor.xml" # Robot scene, /unitree_robots/[robot]/scene.xml

joystick_topic: "/joy"
low_cmd_topic: "/sim/lowcmd" # fsm: "/lowcmd_vita"
low_state_topic: "/sim/lowstate"

rt_low_state_topic: "/rt/lowstate"

limit_pause: false
rt_state_control_mode: false # 是否从 rt_low_state_topic 中提取控制命令（从 relative_motor_cmd 字段）
history_states: 10000 # 存1万个历史状态,步长默认0.002s，即存储历史20s
history_buffer: 100000000 # 存100MB历史数据，与上面那个相比取最小值,vita00模型大概600steps/MB
figure_range: 1.0 # 图表显示的时间跨度，默认绘制1秒的数据
low_state_hz: 500 # 仿真频率，单位Hz

joy_vel_x_factor: 1.0
joy_vel_y_factor: 1.0
joy_ang_factor: 1.0

# vis_options: ["neck_force", "neck_torque", "neck_accel"]

# 减速比配置
gear_ratio_compensation:
  enable: false # 是否启用减速比补偿（输入是否已经除过减速比平方？）
  calf_gear_ratio: 2.0 # 小腿关节的减速比
  calf_joint_indices: [2, 5, 8, 11] # 小腿关节的索引 (FR_calf, FL_calf, RR_calf, RL_calf)
  ssm_kp: 40.0 # 系统状态机的kp值，如果检测到此值则不进行减速比补偿
