<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>vita_sim</name>
  <version>0.0.1</version>
  <description>Vita Simulation Package</description>
  <maintainer email="<EMAIL>">peizhe.chen</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <depend>ament_index_cpp</depend>
  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>unitree_go</depend>
  <depend>lowlevel_msg</depend>
  <depend>vita01b</depend>
  <depend>vita01</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
