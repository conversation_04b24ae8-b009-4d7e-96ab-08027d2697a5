// Copyright 2025 VitaDynamics Limited

#include "vita_sim/robot_controller.h"

#include <chrono>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"

#include "simulate/simulate.h"

#pragma GCC diagnostic pop

namespace {
constexpr int kQuaternionSize = 4;
constexpr int kVectorSize = 3;
constexpr double kResetTimeThreshold = 0.01;  // 10ms
constexpr int kSelectButtonIndex = 6;         // SELECT 按钮的索引

vita_sim::RobotController *instance_ = nullptr;
}  // namespace

namespace vita_sim {

RobotController::RobotController(mujoco::Simulate &sim)
    : Node("vita_sim_controller"),
      sim_(sim),
      last_cmd_time_(this->now()) {
        executor_.add_node(this->get_node_base_interface());
      }

RobotController::~RobotController() {
  // 清除回调函数
  mjcb_control = nullptr;

  // 清除静态实例指针
  if (instance_ == this) {
    instance_ = nullptr;
  }
}

void RobotController::InitController(const SimConfig &sim_config) {
  {
    const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
    if (sim_.d_) {
      last_sim_time_ = sim_.d_->time;
    }
    if (sim_.m_) {
      cmd_timeout_ = sim_.m_->opt.timestep;
    }
  }

  rt_state_control_mode_ = sim_config.rt_state_control_mode;
  limit_pause_ = sim_config.limit_pause;
  gear_ratio_compensation_enable_ =
      sim_config.gear_ratio_compensation_enable;
  calf_gear_ratio_sq_ = sim_config.calf_gear_ratio *
                      sim_config.calf_gear_ratio;
  ssm_kp_ = sim_config.ssm_kp;

  cmd_sub_ = this->create_subscription<LowCmdMsg>(
      sim_config.low_cmd_topic, 1,
      std::bind(&RobotController::CmdCallback, this, std::placeholders::_1));

  joy_sub_ = this->create_subscription<sensor_msgs::msg::Joy>(
      sim_config.joystick_topic, 1,
      std::bind(&RobotController::JoyCallback, this, std::placeholders::_1));

  rt_state_sub_ = this->create_subscription<LowStateMsg>(
      sim_config.rt_low_state_topic, 1,
      std::bind(&RobotController::RtLowCmdCallback, this,
                std::placeholders::_1));

  state_pub_ =
      this->create_publisher<LowStateMsg>(sim_config.low_state_topic, 1);

  const double timestep_sec = 1.0 / sim_config.low_state_hz;
  const auto clamped_sec = std::clamp(timestep_sec, 0.002, 0.1);
  const auto timestep_nsec = static_cast<int64_t>(clamped_sec * 1e9);
  timer_ = this->create_wall_timer(
      std::chrono::nanoseconds(timestep_nsec),
      std::bind(&RobotController::PublishStateCallback, this));

  // 预计算小腿关节标志数组
  for (int i = 0; i < 12; ++i) {
    calf_joint_flags_[i] = sim_config.calf_joint_indices.count(i) > 0;
  }

  // 设置静态实例指针
  instance_ = this;

  InitJointIds();

  // 设置MuJoCo回调函数
  mjcb_control = [](const mjModel *m, mjData *d) {
    if (instance_) {
      if (instance_->initial_state_applied_.load() &&
          instance_->initial_state_msg_) {
        instance_->ApplyInitialJointState(m, d);
        instance_->initial_state_msg_.reset();
      }
      if (instance_->has_custom_joint_pos_) {
        instance_->ApplyJointPositions(m, d);
      }
      instance_->CheckLimits(m, d);
    }
  };
}

void RobotController::CheckLimits(const mjModel *m, mjData *d) {
  if (!has_custom_joint_pos_ || !sim_.run) {
    return;
  }

  const int freejoint_adr = m->jnt_qposadr[0];
  const auto freejoint_pos = d->qpos[freejoint_adr];

  if (std::abs(freejoint_pos) < 0.05) {
    return;
  }

  // 位置限制检查
  constexpr double kJointLimitTolerance = 0.05;
  const auto njnt = m->njnt;
  for (int i = 1; i < njnt; ++i) {
    const int joint_adr = m->jnt_qposadr[i];
    const auto pos = d->qpos[joint_adr];
    const auto lower = m->jnt_range[i * 2];
    const auto upper = m->jnt_range[i * 2 + 1];
    if (pos < lower - kJointLimitTolerance ||
        pos > upper + kJointLimitTolerance) {
      RCLCPP_WARN(this->get_logger(),
                  "Joint %2d pos [%.2f] exceeds [%.2f, %.2f]", i, pos, lower,
                  upper);
      if (limit_pause_) {
        sim_.run = 0;
        RCLCPP_INFO(this->get_logger(),
                    "Simulation paused due to position limit");
        break;
      }
    }
  }
}

void RobotController::ApplyJointPositions(const mjModel *m, mjData *d) {
  if (!has_custom_joint_pos_) {
    return;
  }

  // 获取关节数据的锁
  std::lock_guard<std::mutex> lock(joints_mutex_);

  // FR - 右前腿
  if (ghost_joints_.fr.hip_id != -1 && ghost_joints_.fr.thigh_id != -1 &&
      ghost_joints_.fr.calf_id != -1) {
    int fr_hip_joint_adr = m->jnt_qposadr[ghost_joints_.fr.hip_id];
    int fr_thigh_joint_adr = m->jnt_qposadr[ghost_joints_.fr.thigh_id];
    int fr_calf_joint_adr = m->jnt_qposadr[ghost_joints_.fr.calf_id];

    d->qpos[fr_hip_joint_adr] = ghost_joints_.fr.hip_pos;
    d->qpos[fr_thigh_joint_adr] = ghost_joints_.fr.thigh_pos;
    d->qpos[fr_calf_joint_adr] = ghost_joints_.fr.calf_pos;
  }

  // FL - 左前腿
  if (ghost_joints_.fl.hip_id != -1 && ghost_joints_.fl.thigh_id != -1 &&
      ghost_joints_.fl.calf_id != -1) {
    int fl_hip_joint_adr = m->jnt_qposadr[ghost_joints_.fl.hip_id];
    int fl_thigh_joint_adr = m->jnt_qposadr[ghost_joints_.fl.thigh_id];
    int fl_calf_joint_adr = m->jnt_qposadr[ghost_joints_.fl.calf_id];

    d->qpos[fl_hip_joint_adr] = ghost_joints_.fl.hip_pos;
    d->qpos[fl_thigh_joint_adr] = ghost_joints_.fl.thigh_pos;
    d->qpos[fl_calf_joint_adr] = ghost_joints_.fl.calf_pos;
  }

  // RR - 右后腿
  if (ghost_joints_.rr.hip_id != -1 && ghost_joints_.rr.thigh_id != -1 &&
      ghost_joints_.rr.calf_id != -1) {
    int rr_hip_joint_adr = m->jnt_qposadr[ghost_joints_.rr.hip_id];
    int rr_thigh_joint_adr = m->jnt_qposadr[ghost_joints_.rr.thigh_id];
    int rr_calf_joint_adr = m->jnt_qposadr[ghost_joints_.rr.calf_id];

    d->qpos[rr_hip_joint_adr] = ghost_joints_.rr.hip_pos;
    d->qpos[rr_thigh_joint_adr] = ghost_joints_.rr.thigh_pos;
    d->qpos[rr_calf_joint_adr] = ghost_joints_.rr.calf_pos;
  }

  // RL - 左后腿
  if (ghost_joints_.rl.hip_id != -1 && ghost_joints_.rl.thigh_id != -1 &&
      ghost_joints_.rl.calf_id != -1) {
    int rl_hip_joint_adr = m->jnt_qposadr[ghost_joints_.rl.hip_id];
    int rl_thigh_joint_adr = m->jnt_qposadr[ghost_joints_.rl.thigh_id];
    int rl_calf_joint_adr = m->jnt_qposadr[ghost_joints_.rl.calf_id];

    d->qpos[rl_hip_joint_adr] = ghost_joints_.rl.hip_pos;
    d->qpos[rl_thigh_joint_adr] = ghost_joints_.rl.thigh_pos;
    d->qpos[rl_calf_joint_adr] = ghost_joints_.rl.calf_pos;
  }
}

void RobotController::CmdCallback(const typename LowCmdMsg::SharedPtr msg) {
  if (!msg) {
    return;
  }

  // 如果仿真暂停，不处理控制命令
  if (!sim_.run) {
    return;
  }

  {
    std::lock_guard<std::mutex> lock(cmd_mutex_);
    last_cmd_ = msg;
    last_cmd_time_ = this->now();

    // 如果这是第一帧控制命令，触发初始状态同步
    if (!has_received_cmd_.load() && !initial_state_applied_.load()) {
      RCLCPP_INFO(this->get_logger(),
                  "First control command received, syncing initial state");

      auto rt_state = sim_.agent_->CurrentRtLowState();
      if (rt_state) {
        initial_state_msg_ = std::make_shared<LowStateMsg>(*rt_state);
        initial_state_applied_.store(true);
        RCLCPP_INFO(
            this->get_logger(),
            "Initial state captured, will be applied in next control cycle");
      } else {
        RCLCPP_WARN(this->get_logger(),
                    "No state message received, cannot sync initial state");
      }
    }
  }

  has_received_cmd_.store(true);
  // 在第一次接收到cmd后, 设置图表开始显示的时间
  sim_.agent_->SetSimBeginTime(sim_.d_);

  UpdateControl();
}

void RobotController::RtLowCmdCallback(
    const typename LowStateMsg::SharedPtr msg) {
  if (!msg) {
    return;
  }

  {
    std::lock_guard<std::mutex> lock(cmd_mutex_);
    sim_.agent_->rt_low_state_que_->put(*msg.get());
  }

  // 如果启用了从 rt_state 提取控制命令的模式，则处理控制逻辑
  if (rt_state_control_mode_) {
    ProcessRtStateControl(msg);
  }
}

void RobotController::UpdateControl() {
  if (!has_received_cmd_.load()) {
    return;
  }

  typename LowCmdMsg::SharedPtr current_cmd;
  {
    std::lock_guard<std::mutex> lock(cmd_mutex_);
    if (!last_cmd_) {
      return;
    }
    current_cmd = last_cmd_;
  }

  const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
  if (!sim_.m_ || !sim_.d_) {
    return;
  }
  sim_.agent_->low_cmd_que_->put(*current_cmd.get());
  for (int i = 0; i < sim_.m_->nu; i++) {
    const auto &motor = current_cmd->motor_cmd[i];

    // 根据配置决定是否对小腿关节进行减速比补偿
    double kp = motor.kp;
    double kd = motor.kd;

    // 减速比补偿的条件判断：
    // 1. 功能启用
    // 2. motor.kp != ssm_kp (非系统状态机的命令)
    if (gear_ratio_compensation_enable_ &&
        std::abs(motor.kp - ssm_kp_) > 1e-6) {
      // 检查当前关节是否为小腿关节（使用预计算的标志数组）
      if (calf_joint_flags_[i]) {
        // 对小腿关节的kp/kd进行减速比平方的补偿（使用预计算的平方值）
        // 在真实机器人中，控制器发送的是电机端的kp/kd，被减速比平方降低
        // 在 MuJoCo 中，我们直接控制关节，所以需要乘以减速比平方来补偿
        kp *= calf_gear_ratio_sq_;
        kd *= calf_gear_ratio_sq_;
      }
    }

    const auto torque = motor.tau + kp * (motor.q - sim_.d_->sensordata[i]) +
                        kd * (motor.dq - sim_.d_->sensordata[i + sim_.m_->nu]);

    const auto lower = sim_.m_->actuator_forcerange[i * 2];
    const auto upper = sim_.m_->actuator_forcerange[i * 2 + 1];

    if (torque < lower || torque > upper) {
      RCLCPP_WARN(this->get_logger(),
                  "Joint %2d tau [%.2f] exceeds [%.2f, %.2f]", i, torque, lower,
                  upper);
      if (limit_pause_) {
        sim_.run = 0;
        RCLCPP_INFO(this->get_logger(),
                    "Simulation paused due to torque limit");
        return;
      }
    }
    sim_.d_->ctrl[i] = torque;
  }

  UpdateGhostPosition(current_cmd);
}

void RobotController::UpdateGhostPosition(
    const typename LowCmdMsg::SharedPtr &cmd) {
  has_custom_joint_pos_ = true;

  // 获取关节数据的锁
  std::lock_guard<std::mutex> lock(joints_mutex_);

  // FR - 右前腿 (电机索引 0, 1, 2)
  ghost_joints_.fr.hip_pos = cmd->motor_cmd[0].q;
  ghost_joints_.fr.thigh_pos = cmd->motor_cmd[1].q;
  ghost_joints_.fr.calf_pos = cmd->motor_cmd[2].q;

  // FL - 左前腿 (电机索引 3, 4, 5)
  ghost_joints_.fl.hip_pos = cmd->motor_cmd[3].q;
  ghost_joints_.fl.thigh_pos = cmd->motor_cmd[4].q;
  ghost_joints_.fl.calf_pos = cmd->motor_cmd[5].q;

  // RR - 右后腿 (电机索引 6, 7, 8)
  ghost_joints_.rr.hip_pos = cmd->motor_cmd[6].q;
  ghost_joints_.rr.thigh_pos = cmd->motor_cmd[7].q;
  ghost_joints_.rr.calf_pos = cmd->motor_cmd[8].q;

  // RL - 左后腿 (电机索引 9, 10, 11)
  ghost_joints_.rl.hip_pos = cmd->motor_cmd[9].q;
  ghost_joints_.rl.thigh_pos = cmd->motor_cmd[10].q;
  ghost_joints_.rl.calf_pos = cmd->motor_cmd[11].q;
}

void RobotController::CheckSimulationReset() {
  const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
  if (!sim_.d_) {
    return;
  }

  if (sim_.d_->time < last_sim_time_ - kResetTimeThreshold) {
    RCLCPP_INFO(this->get_logger(),
                "Simulation reset detected! Resetting controller state.");

    {
      std::lock_guard<std::mutex> lock(cmd_mutex_);
      last_cmd_.reset();
      last_cmd_time_ = this->now();
    }

    has_received_cmd_.store(false);

    for (int i = 0; i < sim_.m_->nu; i++) {
      sim_.d_->ctrl[i] = 0.0;
    }

    has_custom_joint_pos_ = false;
    initial_state_applied_.store(false);
    initial_state_msg_.reset();
  }

  last_sim_time_ = sim_.d_->time;
}

void RobotController::PublishStateCallback() {
  CheckSimulationReset();

  // 如果仿真暂停，不发布状态
  if (!sim_.run) {
    return;
  }

  if (has_received_cmd_.load()) {
    auto current_time = this->now();
    auto elapsed = current_time - last_cmd_time_;

    if (elapsed.seconds() > cmd_timeout_) {
      UpdateControl();
    }
  }

  const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
  if (!sim_.m_ || !sim_.d_) {
    return;
  }

  auto msg = std::make_unique<LowStateMsg>();

#ifndef USE_UNITREE_GO
  // 填充状态ID（自增）
  msg->state_id = state_id_++;

  // 填充输入命令ID和相对电机命令
  {
    std::lock_guard<std::mutex> lock(cmd_mutex_);
    if (last_cmd_) {
      msg->input_cmd_id = last_cmd_->cmd_id;
      msg->relative_motor_cmd = last_cmd_->motor_cmd;
    }
  }
#endif

  // 填充电机状态
  for (int i = 0; i < sim_.m_->nu; i++) {
    msg->motor_state[i].q = sim_.d_->sensordata[i];
    msg->motor_state[i].dq = sim_.d_->sensordata[i + sim_.m_->nu];
    msg->motor_state[i].tau_est = sim_.d_->sensordata[i + 2 * sim_.m_->nu];
  }

  // 计算 IMU 数据的起始位置 (所有电机传感器之后)
  int data_offset = sim_.m_->nu * 3;

  // 填充四元数 (w, x, y, z)
  for (int i = 0; i < kQuaternionSize; ++i) {
    msg->imu_state.quaternion[i] = sim_.d_->sensordata[data_offset++];
  }

  // 填充陀螺仪数据 (x, y, z)
  for (int i = 0; i < kVectorSize; ++i) {
    // Convert from rad/s to deg/s (multiply by 180/π)
    // https://mujoco.readthedocs.io/en/latest/overview.html#units-are-unspecified
    msg->imu_state.gyroscope[i] = sim_.d_->sensordata[data_offset++] * 180.0 / M_PI;
  }

  // 填充加速度计数据 (x, y, z)
  for (int i = 0; i < kVectorSize; ++i) {
    // Convert from m/s² to g-units (divide by 9.81)
    // https://mujoco.readthedocs.io/en/latest/overview.html#units-are-unspecified
    msg->imu_state.accelerometer[i] = sim_.d_->sensordata[data_offset++] / 9.81;
  }

  state_pub_->publish(std::move(msg));
}

void RobotController::Spin() {
  // 使用 sim_ 的 exitrequest 作为退出条件
  while (rclcpp::ok() && !sim_.exitrequest.load()) {
    executor_.spin_some(std::chrono::milliseconds(10));
  }
  if (!sim_.exitrequest.load()) {
    sim_.exitrequest.store(1);
  }

  RCLCPP_INFO(this->get_logger(), "RobotController exit");
}

void RobotController::InitJointIds() {
  const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
  if (!sim_.m_) {
    return;
  }

  // 初始化右前腿(FR)关节ID
  ghost_joints_.fr.hip_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FR_hip");
  ghost_joints_.fr.thigh_id =
      mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FR_thigh");
  ghost_joints_.fr.calf_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FR_calf");

  // 初始化左前腿(FL)关节ID
  ghost_joints_.fl.hip_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FL_hip");
  ghost_joints_.fl.thigh_id =
      mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FL_thigh");
  ghost_joints_.fl.calf_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_FL_calf");

  // 初始化右后腿(RR)关节ID
  ghost_joints_.rr.hip_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RR_hip");
  ghost_joints_.rr.thigh_id =
      mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RR_thigh");
  ghost_joints_.rr.calf_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RR_calf");

  // 初始化左后腿(RL)关节ID
  ghost_joints_.rl.hip_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RL_hip");
  ghost_joints_.rl.thigh_id =
      mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RL_thigh");
  ghost_joints_.rl.calf_id = mj_name2id(sim_.m_, mjOBJ_JOINT, "ghost_RL_calf");
}

void RobotController::JoyCallback(const sensor_msgs::msg::Joy::SharedPtr msg) {
  if (!msg || msg->buttons.size() <= kSelectButtonIndex) {
    return;
  }

  sim_.agent_->joy_stick_que_->put(*msg.get());

  if (msg->buttons[kSelectButtonIndex] == 1) {
    const std::unique_lock<std::recursive_mutex> lock(sim_.mtx);
    if (sim_.d_) {
      RCLCPP_INFO(this->get_logger(), "Resetting simulation via SELECT button");
      mj_resetData(sim_.m_, sim_.d_);
      last_sim_time_ = sim_.d_->time;

      // 重置控制器状态
      {
        std::lock_guard<std::mutex> lock(cmd_mutex_);
        last_cmd_.reset();
        last_cmd_time_ = this->now();
      }
      has_received_cmd_.store(false);
      initial_state_applied_.store(false);
      initial_state_msg_.reset();
    }
  }
}

void RobotController::ApplyInitialJointState(const mjModel *m, mjData *d) {
  if (!initial_state_msg_) {
    return;
  }

  RCLCPP_INFO(this->get_logger(), "Apply initial state to actual robot");

  // 应用 IMU 四元数到机器人姿态
  // qpos[0:3] 是位置，qpos[3:7] 是姿态四元数 (w,x,y,z)
  // 注意：这里不修改机器人的位置，只修改姿态
  const auto &imu_quat = initial_state_msg_->imu_state.quaternion;
  d->qpos[3] = imu_quat[0];  // w
  d->qpos[4] = imu_quat[1];  // x
  d->qpos[5] = imu_quat[2];  // y
  d->qpos[6] = imu_quat[3];  // z

  RCLCPP_INFO(this->get_logger(), "Applied IMU quaternion: [%f, %f, %f, %f]",
              imu_quat[0], imu_quat[1], imu_quat[2], imu_quat[3]);

  // 7-9: FR_hip_joint, FR_thigh_joint, FR_calf_joint
  // 10-12: FL_hip_joint, FL_thigh_joint, FL_calf_joint
  // 13-15: RR_hip_joint, RR_thigh_joint, RR_calf_joint
  // 16-18: RL_hip_joint, RL_thigh_joint, RL_calf_joint

  // 获取实际关节ID
  int fr_hip_joint_id = mj_name2id(m, mjOBJ_JOINT, "FR_hip_joint");
  int fr_thigh_joint_id = mj_name2id(m, mjOBJ_JOINT, "FR_thigh_joint");
  int fr_calf_joint_id = mj_name2id(m, mjOBJ_JOINT, "FR_calf_joint");

  int fl_hip_joint_id = mj_name2id(m, mjOBJ_JOINT, "FL_hip_joint");
  int fl_thigh_joint_id = mj_name2id(m, mjOBJ_JOINT, "FL_thigh_joint");
  int fl_calf_joint_id = mj_name2id(m, mjOBJ_JOINT, "FL_calf_joint");

  int rr_hip_joint_id = mj_name2id(m, mjOBJ_JOINT, "RR_hip_joint");
  int rr_thigh_joint_id = mj_name2id(m, mjOBJ_JOINT, "RR_thigh_joint");
  int rr_calf_joint_id = mj_name2id(m, mjOBJ_JOINT, "RR_calf_joint");

  int rl_hip_joint_id = mj_name2id(m, mjOBJ_JOINT, "RL_hip_joint");
  int rl_thigh_joint_id = mj_name2id(m, mjOBJ_JOINT, "RL_thigh_joint");
  int rl_calf_joint_id = mj_name2id(m, mjOBJ_JOINT, "RL_calf_joint");

  // FR - 右前腿
  if (fr_hip_joint_id != -1 && fr_thigh_joint_id != -1 &&
      fr_calf_joint_id != -1) {
    d->qpos[m->jnt_qposadr[fr_hip_joint_id]] =
        initial_state_msg_->motor_state[0].q;
    d->qpos[m->jnt_qposadr[fr_thigh_joint_id]] =
        initial_state_msg_->motor_state[1].q;
    d->qpos[m->jnt_qposadr[fr_calf_joint_id]] =
        initial_state_msg_->motor_state[2].q;
  } else {
    RCLCPP_WARN(this->get_logger(), "Cannot find FR leg joint");
  }

  // FL - 左前腿
  if (fl_hip_joint_id != -1 && fl_thigh_joint_id != -1 &&
      fl_calf_joint_id != -1) {
    d->qpos[m->jnt_qposadr[fl_hip_joint_id]] =
        initial_state_msg_->motor_state[3].q;
    d->qpos[m->jnt_qposadr[fl_thigh_joint_id]] =
        initial_state_msg_->motor_state[4].q;
    d->qpos[m->jnt_qposadr[fl_calf_joint_id]] =
        initial_state_msg_->motor_state[5].q;
  } else {
    RCLCPP_WARN(this->get_logger(), "Cannot find FL leg joint");
  }

  // RR - 右后腿
  if (rr_hip_joint_id != -1 && rr_thigh_joint_id != -1 &&
      rr_calf_joint_id != -1) {
    d->qpos[m->jnt_qposadr[rr_hip_joint_id]] =
        initial_state_msg_->motor_state[6].q;
    d->qpos[m->jnt_qposadr[rr_thigh_joint_id]] =
        initial_state_msg_->motor_state[7].q;
    d->qpos[m->jnt_qposadr[rr_calf_joint_id]] =
        initial_state_msg_->motor_state[8].q;
  } else {
    RCLCPP_WARN(this->get_logger(), "Cannot find RR leg joint");
  }

  // RL - 左后腿
  if (rl_hip_joint_id != -1 && rl_thigh_joint_id != -1 &&
      rl_calf_joint_id != -1) {
    d->qpos[m->jnt_qposadr[rl_hip_joint_id]] =
        initial_state_msg_->motor_state[9].q;
    d->qpos[m->jnt_qposadr[rl_thigh_joint_id]] =
        initial_state_msg_->motor_state[10].q;
    d->qpos[m->jnt_qposadr[rl_calf_joint_id]] =
        initial_state_msg_->motor_state[11].q;
  } else {
    RCLCPP_WARN(this->get_logger(), "Cannot find RL leg joint");
  }

  // 重新计算姿态相关参数以反映位置变化
  mj_kinematics(m, d);
}

void RobotController::ProcessRtStateControl(
    const typename LowStateMsg::SharedPtr &msg) {
  if (!msg) {
    return;
  }

  RCLCPP_DEBUG(this->get_logger(), "Processing control from rt state message");

  // 如果仿真暂停，不处理控制命令
  if (!sim_.run) {
    return;
  }

#ifndef USE_UNITREE_GO
  // 检查是否包含 relative_motor_cmd 数据
  if (msg->relative_motor_cmd.empty()) {
    RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                         "No relative_motor_cmd data in rt state message");
    return;
  }

  // 构造一个 LowCmdMsg，从 relative_motor_cmd 中提取数据
  auto cmd_msg = std::make_shared<LowCmdMsg>();
  cmd_msg->cmd_id = msg->input_cmd_id;

  // 复制电机命令数据
  const size_t motor_count =
      std::min(msg->relative_motor_cmd.size(), cmd_msg->motor_cmd.size());
  for (size_t i = 0; i < motor_count; ++i) {
    cmd_msg->motor_cmd[i] = msg->relative_motor_cmd[i];
  }

  // 处理控制逻辑，类似于 CmdCallback
  {
    std::lock_guard<std::mutex> lock(cmd_mutex_);
    last_cmd_ = cmd_msg;
    last_cmd_time_ = this->now();

    // 如果这是第一帧控制命令，触发初始状态同步
    if (!has_received_cmd_.load() && !initial_state_applied_.load()) {
      RCLCPP_INFO(this->get_logger(),
                  "First rt state control received, syncing initial state");

      auto rt_state = sim_.agent_->CurrentRtLowState();
      if (rt_state) {
        initial_state_msg_ = std::make_shared<LowStateMsg>(*rt_state);
        initial_state_applied_.store(true);
        RCLCPP_INFO(this->get_logger(),
                    "Initial state captured from rt state, will be applied in "
                    "next control cycle");
      } else {
        // 使用当前的 rt state 消息作为初始状态
        initial_state_msg_ = std::make_shared<LowStateMsg>(*msg);
        initial_state_applied_.store(true);
        RCLCPP_INFO(this->get_logger(),
                    "Using current rt state as initial state");
      }
    }
  }

  has_received_cmd_.store(true);
  // 在第一次接收到cmd后, 设置图表开始显示的时间
  sim_.agent_->SetSimBeginTime(sim_.d_);

  UpdateControl();
#else
  RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                       "rt_state_control_mode not supported for UNITREE_GO");
#endif
}

}  // namespace vita_sim
