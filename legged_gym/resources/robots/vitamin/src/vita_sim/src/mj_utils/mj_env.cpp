// Copyright 2025 VitaDynamics Limited

#include "vita_sim/mj_utils/mj_env.h"

#include <signal.h>

#include <mutex>

// ROS
#include <ament_index_cpp/get_package_share_directory.hpp>

// Mujoco
#include <mujoco/mjdata.h>
#include <mujoco/mjmodel.h>
#include <mujoco/mujoco.h>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wsign-compare"
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"

#include "simulate/array_safety.h"
#include "simulate/glfw_adapter.h"
#include "simulate/simulate.h"

#pragma GCC diagnostic pop

#include "vita_sim/robot_controller.h"

namespace {
namespace mj = ::mujoco;
namespace mju = ::mujoco::sample_util;

// 使用Simulate中定义的MutexLock类型
using MutexLock = std::unique_lock<std::recursive_mutex>;

// maximum mis-alignment before re-sync (simulation seconds)
const double kSyncMisalign = 0.1;
// fraction of refresh available for simulation
const double kSimRefreshFraction = 0.7;
// load error string length
const int kErrorLength = 1024;

using Seconds = std::chrono::duration<double>;

bool CheckMujocoVersion() {
  if (mjVERSION_HEADER != mj_version()) {
    mju_error("[Mujoco] version mismatch: %d != %d", mjVERSION_HEADER,
              mj_version());
    return false;
  }
  return true;
}

void ScanPluginLibraries() {
  int plugin_count = mjp_pluginCount();
  if (plugin_count != 0) {
    printf("[Mujoco] Built-in plugins: \n");
    for (int i = 0; i < plugin_count; ++i) {
      printf("  %s\n", mjp_getPluginAtSlot(i)->name);
    }
  }

  const std::string shared_dir =
      ament_index_cpp::get_package_share_directory("vita_sim");
  mj_loadAllPluginLibraries(
      shared_dir.c_str(), +[](const char *filename, int first, int count) {
        printf("Plugins registered by library '%s':\n", filename);
        for (int i = first; i < first + count; ++i) {
          printf("  %s\n", mjp_getPluginAtSlot(i)->name);
        }
      });
}

const char *Diverged(int disableflags, const mjData *d) {
  if (disableflags & mjDSBL_AUTORESET) {
    for (mjtWarning w : {mjWARN_BADQACC, mjWARN_BADQVEL, mjWARN_BADQPOS}) {
      if (d->warning[w].number > 0) {
        return mju_warningText(w, d->warning[w].lastinfo);
      }
    }
  }
  return nullptr;
}

mjModel *LoadModel(const char *file, mj::Simulate &sim) {
  // this copy is needed so that the mju::strlen call below compiles
  char filename[mj::Simulate::kMaxFilenameLength];
  mju::strcpy_arr(filename, file);

  // make sure filename is not empty
  if (!filename[0]) {
    return nullptr;
  }

  // load and compile
  char loadError[kErrorLength] = "";
  mjModel *mnew = 0;
  auto load_start = mj::Simulate::Clock::now();
  if (mju::strlen_arr(filename) > 4 &&
      !std::strncmp(
          filename + mju::strlen_arr(filename) - 4, ".mjb",
          mju::sizeof_arr(filename) - mju::strlen_arr(filename) + 4)) {
    mnew = mj_loadModel(filename, nullptr);
    if (!mnew) {
      mju::strcpy_arr(loadError, "could not load binary model");
    }
  } else {
    mnew = mj_loadXML(filename, nullptr, loadError, kErrorLength);

    // remove trailing newline character from loadError
    if (loadError[0]) {
      int error_length = mju::strlen_arr(loadError);
      if (loadError[error_length - 1] == '\n') {
        loadError[error_length - 1] = '\0';
      }
    }
  }
  auto load_interval = mj::Simulate::Clock::now() - load_start;
  double load_seconds = Seconds(load_interval).count();

  if (!mnew) {
    std::printf("%s\n", loadError);
    mju::strcpy_arr(sim.load_error, loadError);
    return nullptr;
  }

  // compiler warning: print and pause
  if (loadError[0]) {
    // mj_forward() below will print the warning message
    std::printf("Model compiled, but simulation warning (paused):\n  %s\n",
                loadError);
    sim.run = 0;
  } else if (load_seconds > 0.25) {
    // if no error and load took more than 1/4 seconds, report load time
    mju::sprintf_arr(loadError, "Model loaded in %.2g seconds", load_seconds);
  }

  mju::strcpy_arr(sim.load_error, loadError);

  return mnew;
}

bool IsMisaligned(const std::chrono::time_point<mj::Simulate::Clock> &start_cpu,
                  const std::chrono::time_point<mj::Simulate::Clock> &sync_cpu,
                  double elapsed_sim, double slowdown) {
  const auto elapsed_cpu = start_cpu - sync_cpu;
  return std::abs(Seconds(elapsed_cpu).count() / slowdown - elapsed_sim) >
         kSyncMisalign;
}

bool NeedResync(const std::chrono::time_point<mj::Simulate::Clock> &start_cpu,
                const std::chrono::time_point<mj::Simulate::Clock> &sync_cpu,
                double elapsed_sim, bool speed_changed) {
  const auto elapsed_cpu = start_cpu - sync_cpu;
  return elapsed_sim < 0 || elapsed_cpu.count() < 0 ||
         sync_cpu.time_since_epoch().count() == 0 || speed_changed;
}

bool StepSimulation(mj::Simulate &sim, bool &stepped) {
  mj_step(sim.m_, sim.d_);
  const char *message = Diverged(sim.m_->opt.disableflags, sim.d_);
  if (message) {
    sim.run = 0;
    mju::strcpy_arr(sim.load_error, message);
    return false;
  }
  stepped = true;
  return true;
}

void HandleInSyncSimulation(
    mj::Simulate &sim,
    const std::chrono::time_point<mj::Simulate::Clock> &start_cpu,
    const std::chrono::time_point<mj::Simulate::Clock> &sync_cpu,
    double sync_sim, double slowdown, double elapsed_sim, bool &stepped) {
  bool measured = false;
  mjtNum prev_sim = sim.d_->time;
  double refresh_time = kSimRefreshFraction / sim.refresh_rate;

  // step while sim lags behind cpu and within refreshTime
  while (Seconds((sim.d_->time - sync_sim) * slowdown) <
             mj::Simulate::Clock::now() - sync_cpu &&
         mj::Simulate::Clock::now() - start_cpu < Seconds(refresh_time)) {
    // measure slowdown before first step
    if (!measured && elapsed_sim) {
      const auto elapsed_cpu = start_cpu - sync_cpu;
      sim.measured_slowdown =
          std::chrono::duration<double>(elapsed_cpu).count() / elapsed_sim;
      measured = true;
    }

    // inject noise
    sim.InjectNoise();

    // step simulation
    if (!StepSimulation(sim, stepped)) {
      return;
    }

    if (sim.d_->time < prev_sim) {
      break;
    }
  }
}

void PhysicsLoop(mj::Simulate &sim) {
  std::chrono::time_point<mj::Simulate::Clock> sync_cpu;
  mjtNum sync_sim = 0;

  while (!sim.exitrequest.load()) {
    if (sim.run && sim.busywait) {
      std::this_thread::yield();
    } else {
      std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    const std::unique_lock<std::recursive_mutex> lock(sim.mtx);

    if (!sim.m_) {
      continue;
    }

    if (!sim.run) {
      mj_forward(sim.m_, sim.d_);
      sim.speed_changed = true;
      continue;
    }

    bool stepped = false;
    const auto start_cpu = mj::Simulate::Clock::now();
    double elapsed_sim = sim.d_->time - sync_sim;
    double slowdown = 100 / sim.percentRealTime[sim.real_time_index];

    bool need_resync =
        NeedResync(start_cpu, sync_cpu, elapsed_sim, sim.speed_changed) ||
        IsMisaligned(start_cpu, sync_cpu, elapsed_sim, slowdown);

    sim.agent_->ApplyForceOnSelBody(sim.m_, sim.d_);

    if (need_resync) {
      sync_cpu = start_cpu;
      sync_sim = sim.d_->time;
      sim.speed_changed = false;

      if (!StepSimulation(sim, stepped)) {
        continue;
      }
    } else {
      HandleInSyncSimulation(sim, start_cpu, sync_cpu, sync_sim, slowdown,
                             elapsed_sim, stepped);
    }

    if (stepped) {
      sim.agent_->UpdateLowCmd();
      sim.agent_->UpdateRtLowState();
      sim.agent_->UpdateJoyStick();
      sim.agent_->UpdateGhostQpos(sim.d_);
      sim.agent_->UpdateRtVisQpos(sim.d_);
      sim.AddToHistory();
      sim.agent_->AddState2Hist(sim.m_, sim.d_);
    }
  }
}

void PhysicsThread(mj::Simulate *sim, const char *filename) {
  // request loadmodel if file given (otherwise drag-and-drop)
  if (filename != nullptr) {
    sim->LoadMessage(filename);

    // 使用局部变量暂存指针
    mjModel *m = LoadModel(filename, *sim);
    if (m) {
      mjData *d = mj_makeData(m);
      if (d) {
        sim->Load(m, d, filename);
        mj_forward(sim->m_, sim->d_);

      } else {
        if (m) {
          mj_deleteModel(m);
        }
        sim->LoadMessageClear();
      }
    }
  }

  PhysicsLoop(*sim);

  // 清理资源
  {
    const std::unique_lock<std::recursive_mutex> lock(sim->mtx);
    if (sim->d_) {
      mj_deleteData(sim->d_);
      sim->d_ = nullptr;
    }
    if (sim->m_) {
      mj_deleteModel(sim->m_);
      sim->m_ = nullptr;
    }
  }
}

}  // namespace

namespace mujoco {
Simulator::Simulator() {
  SetupSignalHandlers();
  CheckMujocoVersion();
  ScanPluginLibraries();

  mjv_defaultCamera(&cam_);
  mjv_defaultOption(&opt_);
  mjv_defaultPerturb(&pert_);

  agent_ = std::make_shared<vita_sim::Agent>();
  sim_ = std::make_unique<mj::Simulate>(std::make_unique<mj::GlfwAdapter>(),
                                        &cam_, &opt_, &pert_, agent_,
                                        /* is_passive = */ false);
}
Simulator::~Simulator() {}

void Simulator::Start(const vita_sim::SimConfig &sim_config) {
  const auto &model_file = vita_sim::ScenePath(sim_config);
  printf("model: %s\n", model_file.c_str());
  vita_sim::PrintTopic(sim_config);
  agent_->InitAgentConfig(sim_config,
                          sim_->platform_ui->GetDisplayPixelsPerInch());

  physics_thread_ = std::make_unique<std::thread>(
      [this, model_file]() { PhysicsThread(sim_.get(), model_file.c_str()); });

  user_thread_ = std::make_unique<std::thread>([this, &sim_config]() {
    vita_sim::RobotController controller(*sim_);
    while (true) {
      {
        MutexLock lock(sim_->mtx);
        if (sim_->exitrequest.load()) return;
        if (this->exit_requested_.load()) {
          if (!sim_->exitrequest.load()) {
            std::cout << "Exit requested, exit UI\n";
            sim_->exitrequest.store(true);
          }
          return;
        }
        if (sim_->m_ && sim_->d_) {
          printf(
              "Model and data are fully loaded, motor count: %d, sensor "
              "count: %d\n",
              sim_->m_->nu, sim_->m_->nsensor);
          break;
        }
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    if (model_callback_) {
      model_callback_(*sim_);
    }
    controller.InitController(sim_config);
    controller.Spin();
  });

  sim_->RenderLoop();

  user_thread_->join();
  physics_thread_->join();
}

void Simulator::SetupSignalHandlers() {
  struct sigaction sa;
  sa.sa_handler = &Simulator::StaticSignalHandler;
  sigaction(SIGINT, &sa, nullptr);
  sigaction(SIGTERM, &sa, nullptr);
  instance_.store(this);
}

void Simulator::HandleSignal(int signum) {
  exit_requested_.store(true);
  sim_->exitrequest.store(true);
  std::cout << "receive keyboard interrupt, signum: " << signum
            << ", exiting..." << std::endl;
}

void Simulator::SetModelCallback(std::function<void(Simulate &)> cb) {
  model_callback_ = std::move(cb);
}

}  // namespace mujoco
