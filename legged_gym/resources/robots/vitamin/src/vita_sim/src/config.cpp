// Copyright 2025 VitaDynamics Limited

#include "vita_sim/config.h"

#include <filesystem>
#include <functional>
#include <iostream>

// ROS
#include <ament_index_cpp/get_package_prefix.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"
#include <yaml-cpp/yaml.h>
#pragma GCC diagnostic pop

namespace vita_sim {

// clang-format off
#define CONFIG_ENTRY(name, type) { \
  #name, [](const YAML::Node &config, SimConfig &sim_config) { \
  assignIfExists(config, #name, sim_config.name); \
}}
// clang-format on

template <typename T>
void assignIfExists(const YAML::Node &config, const std::string &key,
                    T &target) {
  if (config[key]) {
    target = config[key].as<T>();
  }
}

SimConfig LoadConfig(const std::string &config_file) {
  std::string path = ament_index_cpp::get_package_share_directory("vita_sim");
  SimConfig sim_config;

  try {
    std::string yaml_path = path + "/" + config_file;
    YAML::Node config = YAML::LoadFile(yaml_path);

    std::unordered_map<std::string,
                       std::function<void(const YAML::Node &, SimConfig &)>>
        configMap = {
            CONFIG_ENTRY(robot, std::string),
            CONFIG_ENTRY(robot_scene, std::string),
            CONFIG_ENTRY(joystick_topic, std::string),
            CONFIG_ENTRY(low_cmd_topic, std::string),
            CONFIG_ENTRY(low_state_topic, std::string),
            CONFIG_ENTRY(rt_low_state_topic, std::string),
            CONFIG_ENTRY(limit_pause, bool),
            CONFIG_ENTRY(rt_state_control_mode, bool),
            CONFIG_ENTRY(history_states, int),
            CONFIG_ENTRY(history_buffer, int),
            CONFIG_ENTRY(figure_range, float),
            CONFIG_ENTRY(low_state_hz, int),
            CONFIG_ENTRY(joy_vel_x_factor, float),
            CONFIG_ENTRY(joy_vel_y_factor, float),
            CONFIG_ENTRY(joy_ang_factor, float),
            CONFIG_ENTRY(vis_options, std::vector<std::string>),
        };

    // 处理嵌套的 gear_ratio_compensation 配置
    if (config["gear_ratio_compensation"]) {
      const auto gear_config = config["gear_ratio_compensation"];
      assignIfExists(gear_config, "enable",
                     sim_config.gear_ratio_compensation_enable);
      assignIfExists(gear_config, "calf_gear_ratio",
                     sim_config.calf_gear_ratio);
      assignIfExists(gear_config, "ssm_kp", sim_config.ssm_kp);

      // 特殊处理 calf_joint_indices，从 vector 转换为 unordered_set
      if (gear_config["calf_joint_indices"]) {
        auto indices_vector =
            gear_config["calf_joint_indices"].as<std::vector<int>>();
        sim_config.calf_joint_indices = std::unordered_set<int>(
            indices_vector.begin(), indices_vector.end());
      }
    }

    for (const auto &pair : configMap) {
      pair.second(config, sim_config);
    }
  } catch (const YAML::Exception &e) {
    std::cerr << "Warning: Failed to load config file '" << config_file
              << "': " << e.what() << std::endl;
    std::cerr << "Using default config..." << std::endl;
  }

  return sim_config;
}

std::string ScenePath(const SimConfig &config) {
  std::string path = ament_index_cpp::get_package_share_directory(config.robot);
  return path + "/" + config.robot_scene;
}

void PrintTopic(const SimConfig &config) {
  std::cout << "Joystick topic: " << config.joystick_topic << std::endl;
  std::cout << "Low cmd topic: " << config.low_cmd_topic << std::endl;
  std::cout << "Low state topic: " << config.low_state_topic << std::endl;
  std::cout << "RT state topic: " << config.rt_low_state_topic << std::endl;
  std::cout << "RT state control mode: "
            << (config.rt_state_control_mode ? "enabled" : "disabled")
            << std::endl;
  std::cout << "Low state Hz: " << config.low_state_hz << std::endl;
}

}  // namespace vita_sim
