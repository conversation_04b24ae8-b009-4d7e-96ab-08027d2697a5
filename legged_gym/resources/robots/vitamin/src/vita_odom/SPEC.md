# vita_odom 规格说明

## 目标

- 实现基于IMU和足部接触状态的机器人里程计
- 直接利用Unitree Go2Estimator算法
- 通过ROS2接口提供位置和姿态估计
- 支持Unitree Go2/Vita四足机器人

## 参考实现

- [unitree_guide](../../unitree_guide/)中的Go2Estimator类

## 实现方案与评估

该项目实现了基于IMU和足部接触状态的四足机器人里程计，具体特点如下：

1. **核心实现**：
   - 直接使用unitree_guide中的Go2Estimator类
   - 采用ROS2接口发布里程计数据
   - 使用足部力传感器数据判断足部接触状态

2. **系统集成**：
   - 订阅LowState消息，获取IMU数据和足部力传感器数据
   - 发布标准nav_msgs/Odometry消息和TF变换
   - 足部接触力阈值设为40.0，可根据实际情况调整

3. **预期表现**：
   - 在足部接触地面时提供更稳定的位置估计
   - 通过IMU积分获取速度和位置
   - 结合足部接触状态减少位置漂移

4. **限制与约束**：
   - 依赖足部力传感器的准确性
   - 对IMU数据质量有较高要求
   - 实际测试中可能存在机器人"裹足不前"的问题，需继续调试

## 后续改进方向

- 优化足部接触状态的识别算法
- 改进IMU数据处理，减少漂移
- 添加自适应阈值调整
- 完善异常处理和错误检测

----

## 足部接触状态的识别算法

- <https://github.com/iit-DLSLab/muse> 中提出一个计算的方法

但它只有头文件: <https://github.com/iit-DLSLab/muse/blob/main/muse_ws/src/iit_commons/include/iit/commons/dog/feet_contact_forces.h>

没有看到具体的实现。只能说参考其方法论。

![论文原文](./muse.png)

有一个可能的实现: <https://github.com/ori-drs/pronto_anymal_example/blob/master/pronto_anymal_b_commons/src/feet_contact_forces.cpp>

我感觉这方面 MUSE 应该是严重参考了 <https://github.com/ori-drs/pronto> 的代码实现。