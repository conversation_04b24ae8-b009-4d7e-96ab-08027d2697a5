// Copyright 2025 VitaDynamics Limited

#pragma once

#include <Eigen/Dense>
#include <cmath>
#include <iostream>

namespace vita_odom {

// 基本数据类型定义
using Vec2 = Eigen::Vector2d;
using Vec3 = Eigen::Vector3d;
using Vec4 = Eigen::Vector4d;
using Vec6 = Eigen::Matrix<double, 6, 1>;
using Vec12 = Eigen::Matrix<double, 12, 1>;
using Vec18 = Eigen::Matrix<double, 18, 1>;
using Vec34 = Eigen::Matrix<double, 3, 4>;
using VecInt4 = Eigen::Matrix<int, 4, 1>;

using Mat3 = Eigen::Matrix3d;
using Mat34 = Eigen::Matrix<double, 3, 4>;
using Mat18 = Eigen::Matrix<double, 18, 18>;
using Mat28_18 = Eigen::Matrix<double, 28, 18>;

// 坐标系类型
enum class FrameType {
  BODY,  // 机体坐标系
  HIP,   // 髋关节坐标系
  GLOBAL // 全局坐标系
};

// 单位矩阵常量
const Mat3 I3 = Mat3::Identity();
const Eigen::Matrix<double, 18, 18> I18 =
    Eigen::Matrix<double, 18, 18>::Identity();
const Eigen::Matrix<double, 12, 12> I12 =
    Eigen::Matrix<double, 12, 12>::Identity();

/**
 * @brief 创建斜对称矩阵(用于叉积运算)
 * @param v 3维向量
 * @return 斜对称矩阵
 */
inline Mat3 skew(const Vec3 &v) {
  Mat3 m;
  m << 0, -v(2), v(1), v(2), 0, -v(0), -v(1), v(0), 0;
  return m;
}

/**
 * @brief 窗函数用于平滑过渡（Unitree Guide实现）
 * @param x 输入值
 * @param windowRatio 窗口比例
 * @param xRange x范围
 * @param yRange y范围
 * @return 平滑后的值
 */
template <typename T>
inline T windowFunc(const T x, const T windowRatio, const T xRange = 1.0,
                    const T yRange = 1.0) {
  if ((x < 0) || (x > xRange)) {
    std::cout << "[ERROR][windowFunc] The x=" << x
              << ", which should between [0, xRange]" << std::endl;
  }
  if ((windowRatio <= 0) || (windowRatio >= 0.5)) {
    std::cout << "[ERROR][windowFunc] The windowRatio=" << windowRatio
              << ", which should between [0, 0.5]" << std::endl;
  }

  if (x / xRange < windowRatio) {
    return x * yRange / (xRange * windowRatio);
  } else if (x / xRange > 1 - windowRatio) {
    return yRange * (xRange - x) / (xRange * windowRatio);
  } else {
    return yRange;
  }
}

} // namespace vita_odom
