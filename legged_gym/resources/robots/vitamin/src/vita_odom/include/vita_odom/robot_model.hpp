// Copyright 2025 VitaDynamics Limited

#pragma once

#include <iostream>

#include "vita_odom/lowlevel_state.hpp"
#include "vita_odom/robot_leg.hpp"
#include "vita_odom/unitree_types.hpp"

namespace vita_odom {

/**
 * @brief 四足机器人基类
 */
class QuadrupedRobot {
public:
  /**
   * @brief 获取机器人X参考点(前右腿位置)
   * @param state 低级别状态
   * @return X参考点
   */
  Vec3 getX(LowlevelState &state) {
    return getFootPosition(state, 0, FrameType::BODY);
  }

  /**
   * @brief 获取相对于X参考点的其他足端位置
   * @param state 低级别状态
   * @return 足端位置矩阵
   */
  Vec34 getVecXP(LowlevelState &state) {
    Vec3 x = getX(state);
    Vec34 vecXP, qLegs;
    qLegs = state.getQ();

    for (int i(0); i < 4; ++i) {
      vecXP.col(i) = legs_[i]->calcPEe2B(qLegs.col(i)) - x;
    }
    return vecXP;
  }

  /**
   * @brief 获取关节角度(逆向运动学)
   * @param vecP 足端位置矩阵
   * @param frame 参考坐标系
   * @return 关节角度
   */
  Vec12 getQ(const Vec34 &vecP, FrameType frame) {
    Vec12 q;
    for (int i(0); i < 4; ++i) {
      q.segment(3 * i, 3) = legs_[i]->calcQ(vecP.col(i), frame);
    }
    return q;
  }

  /**
   * @brief 获取关节角速度
   * @param pos 足端位置矩阵
   * @param vel 足端速度矩阵
   * @param frame 参考坐标系
   * @return 关节角速度
   */
  Vec12 getQd(const Vec34 &pos, const Vec34 &vel, FrameType frame) {
    Vec12 qd;
    for (int i(0); i < 4; ++i) {
      qd.segment(3 * i, 3) = legs_[i]->calcQd(pos.col(i), vel.col(i), frame);
    }
    return qd;
  }

  /**
   * @brief 获取关节力矩
   * @param q 关节角度
   * @param feetForce 足端力矩阵
   * @return 关节力矩
   */
  Vec12 getTau(const Vec12 &q, const Vec34 feetForce) {
    Vec12 tau;
    for (int i(0); i < 4; ++i) {
      tau.segment(3 * i, 3) =
          legs_[i]->calcTau(q.segment(3 * i, 3), feetForce.col(i));
    }
    return tau;
  }

  /**
   * @brief 获取足端位置
   * @param state 低级别状态
   * @param id 腿ID
   * @param frame 参考坐标系
   * @return 足端位置
   */
  Vec3 getFootPosition(const LowlevelState &state, int id, FrameType frame) {
    Vec34 qLegs = state.getQ();

    if (frame == FrameType::BODY) {
      return legs_[id]->calcPEe2B(qLegs.col(id));
    } else if (frame == FrameType::HIP) {
      return legs_[id]->calcPEe2H(qLegs.col(id));
    } else {
      std::cerr << "[ERROR] The frame of function: getFootPosition can only be "
                   "BODY or HIP."
                << std::endl;
      exit(-1);
    }
  }

  /**
   * @brief 获取足端速度
   * @param state 低级别状态
   * @param id 腿ID
   * @return 足端速度
   */
  Vec3 getFootVelocity(LowlevelState &state, int id) {
    Vec34 qLegs = state.getQ();
    Vec34 qdLegs = state.getQd();
    return legs_[id]->calcVEe(qLegs.col(id), qdLegs.col(id));
  }

  /**
   * @brief 获取所有足端相对于机身的位置
   * @param state 低级别状态
   * @param frame 参考坐标系
   * @return 足端位置矩阵
   */
  Vec34 getFeet2BPositions(LowlevelState &state, FrameType frame) {
    Vec34 feetPos;
    if (frame == FrameType::GLOBAL) {
      for (int i(0); i < 4; ++i) {
        feetPos.col(i) = getFootPosition(state, i, FrameType::BODY);
      }
      feetPos = state.getRotMat() * feetPos;
    } else if ((frame == FrameType::BODY) || (frame == FrameType::HIP)) {
      for (int i(0); i < 4; ++i) {
        feetPos.col(i) = getFootPosition(state, i, frame);
      }
    } else {
      std::cerr << "[ERROR] Frame error of function getFeet2BPositions"
                << std::endl;
      exit(-1);
    }
    return feetPos;
  }

  /**
   * @brief 获取所有足端相对于机身的速度
   * @param state 低级别状态
   * @param frame 参考坐标系
   * @return 足端速度矩阵
   */
  Vec34 getFeet2BVelocities(LowlevelState &state, FrameType frame) {
    Vec34 feetVel;
    for (int i(0); i < 4; ++i) {
      feetVel.col(i) = getFootVelocity(state, i);
    }

    if (frame == FrameType::GLOBAL) {
      Vec34 feetPos = getFeet2BPositions(state, FrameType::BODY);
      feetVel += skew(state.imu.getGyro()) * feetPos;
      return state.getRotMat() * feetVel;
    } else if ((frame == FrameType::BODY) || (frame == FrameType::HIP)) {
      return feetVel;
    } else {
      std::cerr << "[ERROR] Frame error of function getFeet2BVelocities"
                << std::endl;
      exit(-1);
    }
  }

  /**
   * @brief 获取雅可比矩阵
   * @param state 低级别状态
   * @param legID 腿ID
   * @return 雅可比矩阵
   */
  Mat3 getJaco(const LowlevelState &state, int legID) {
    return legs_[legID]->calcJaco(state.getQ().col(legID));
  }

  /**
   * @brief 获取腿部总质量
   * @param legID 腿ID
   * @return 腿部总质量
   */
  float getLegMass(int legID) const { return legs_[legID]->getTotalMass(); }

  /**
   * @brief 获取膝关节连杆长度
   * @param legID 腿ID
   * @return 膝关节连杆长度
   */
  float getKneeLinkLength(int legID) const {
    return legs_[legID]->getKneeLinkLength();
  }

  /**
   * @brief 获取小腿质量
   * @param legID 腿ID
   * @return 小腿质量
   */
  float getCalfMass(int legID) const { return legs_[legID]->getCalfMass(); }

  /**
   * @brief 获取机器人总质量
   * @return 机器人总质量
   */
  float getTotalMass() const {
    float total = 0.0;
    for (int i = 0; i < 4; ++i) {
      total += legs_[i]->getTotalMass();
    }
    return total + trunkMass_;
  }

  /**
   * @brief 获取机身质量
   * @return 机身质量
   */
  float getTrunkMass() const { return trunkMass_; }

protected:
  QuadrupedLeg *legs_[4];   // 机器人腿部对象数组
  float trunkMass_ = 7.119; // 机身质量，默认为Vita00机身质量
};

/**
 * @brief Go2机器人类
 */
class Go2Robot : public QuadrupedRobot {
public:
  /**
   * @brief 构造函数
   */
  Go2Robot() {
    // 初始化各腿实例
    legs_[0] = new Go2Leg(0, Vec3(0.1934, -0.0465, 0));  // FR
    legs_[1] = new Go2Leg(1, Vec3(0.1934, 0.0465, 0));   // FL
    legs_[2] = new Go2Leg(2, Vec3(-0.1934, -0.0465, 0)); // RR
    legs_[3] = new Go2Leg(3, Vec3(-0.1934, 0.0465, 0));  // RL

    // 设置Go2机身质量
    trunkMass_ = 6.921;
  }
};

/**
 * @brief Vita00机器人类
 */
class Vita00Robot : public QuadrupedRobot {
public:
  /**
   * @brief 构造函数
   */
  Vita00Robot() {
    // 初始化各腿实例
    legs_[0] = new Vita00Leg(0, Vec3(0.206, -0.05, 0));  // FR
    legs_[1] = new Vita00Leg(1, Vec3(0.206, 0.05, 0));   // FL
    legs_[2] = new Vita00Leg(2, Vec3(-0.206, -0.05, 0)); // RR
    legs_[3] = new Vita00Leg(3, Vec3(-0.206, 0.05, 0));  // RL

    // 设置Vita00机身质量 (从URDF提取)
    trunkMass_ = 7.119;
  }
};

/**
 * @brief Vita01机器人类
 */
class Vita01Robot : public QuadrupedRobot {
public:
  /**
   * @brief 构造函数
   */
  Vita01Robot() {
    // 初始化各腿实例
    legs_[0] = new Vita01Leg(0, Vec3(0.18425, -0.046, 0));  // FR
    legs_[1] = new Vita01Leg(1, Vec3(0.18425, 0.046, 0));   // FL
    legs_[2] = new Vita01Leg(2, Vec3(-0.18425, -0.046, 0)); // RR
    legs_[3] = new Vita01Leg(3, Vec3(-0.18425, 0.046, 0));  // RL

    // 设置Vita01机身质量 (从URDF提取)
    trunkMass_ = 8.078580;
  }
};

/**
 * @brief Vita01b机器人类
 */
class Vita01bRobot : public QuadrupedRobot {
public:
  /**
   * @brief 构造函数
   */
  Vita01bRobot() {
    // 初始化各腿实例
    legs_[0] = new Vita01bLeg(0, Vec3(0.1875, -0.051, 0));  // FR
    legs_[1] = new Vita01bLeg(1, Vec3(0.1875, 0.051, 0));   // FL
    legs_[2] = new Vita01bLeg(2, Vec3(-0.1875, -0.051, 0)); // RR
    legs_[3] = new Vita01bLeg(3, Vec3(-0.1875, 0.051, 0));  // RL

    // 设置Vita01b机身质量 (从URDF base link质量)
    trunkMass_ = 9.642602;
  }
};

} // namespace vita_odom
