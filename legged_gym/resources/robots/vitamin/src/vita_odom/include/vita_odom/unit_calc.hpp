// Copyright 2025 VitaDynamics Limited

#pragma once

#include <cmath>
namespace vita_odom {

/**
 * @brief 将角速度从度/秒转换为弧度/秒
 * @param deg_per_sec 角速度（度/秒）
 * @return 角速度（弧度/秒）
 */
inline double DegPerSecToRadPerSec(double deg_per_sec) {
  return deg_per_sec * M_PI / 180.0;
}

/**
 * @brief 将重力加速度g转换为米/秒^2
 * @param g_value 重力加速度值（g）
 * @return 加速度值（米/秒^2）
 */
inline double GToMetersPerSecSquared(double g_value) {
  constexpr double GRAVITY = 9.81; // 标准重力加速度（米/秒^2）
  return g_value * GRAVITY;
}

} // namespace vita_odom
