// Copyright 2025 VitaDynamics Limited

#include "vita_odom/system_utils.hpp"

#ifdef __linux__
#include <sched.h>
#include <cerrno>
#include <cstring>
#endif

namespace vita_odom {
namespace system_utils {

bool SetCpuAffinity(int cpu_core) {
#ifdef __linux__
  if (cpu_core < 0) {
    return true;  // No affinity setting requested
  }

  cpu_set_t cpuset;
  CPU_ZERO(&cpuset);
  CPU_SET(cpu_core, &cpuset);

  if (sched_setaffinity(0, sizeof(cpu_set_t), &cpuset) == -1) {
    return false;
  }

  return true;
#else
  // Not supported on non-Linux platforms
  (void)cpu_core;  // Suppress unused parameter warning
  return false;
#endif
}

}  // namespace system_utils
}  // namespace vita_odom