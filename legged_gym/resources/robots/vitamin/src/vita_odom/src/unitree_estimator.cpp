// Copyright 2025 VitaDynamics Limited

#include "vita_odom/unitree_estimator.hpp"

#include <chrono>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>

// yaml
#include <yaml-cpp/yaml.h>

namespace vita_odom {

Estimator::Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
                     VecInt4 *contact, Vec4 *phase, double dt, Vec18 Qdig,
                     std::string est_name, rclcpp::Logger logger)
    : robot_model_(robot_model),
      low_state_(low_state),
      contact_(contact),
      phase_(phase),
      dt_(dt),
      Qdig_(Qdig),
      est_name_(est_name),
      logger_(logger) {
  initSystem();
}

Estimator::Estimator(QuadrupedRobot *robot_model, LowlevelState *low_state,
                     VecInt4 *contact, Vec4 *phase, double dt,
                     rclcpp::Logger logger)
    : robot_model_(robot_model),
      low_state_(low_state),
      contact_(contact),
      phase_(phase),
      dt_(dt),
      logger_(logger) {
  // 设置默认的过程噪声
  Qdig_ = Vec18::Zero();
  for (int i(0); i < Qdig_.rows(); ++i) {
    if (i < 3) {
      Qdig_(i) = 0.0003;
    } else if (i < 6) {
      Qdig_(i) = 0.0003;
    } else {
      Qdig_(i) = 0.01;
    }
  }

  est_name_ = "current";
  initSystem();
}

Estimator::~Estimator() {
  delete RCheck_;
  delete uCheck_;
  delete vxFilter_;
  delete vyFilter_;
  delete vzFilter_;
}

void Estimator::initSystem() {
  // 初始化重力向量和大方差值
  g_ << 0, 0, -9.81;
  large_variance_ = 100;

  // 初始化状态向量和控制输入
  xhat_.setZero();
  u_.setZero();
  acc_offset_ = Eigen::Vector3d::Zero();
  acc_offset_ << 0.0, 0.0, 9.81;
  acc_scale_ = 1.0;  // 默认scale为1.0

  // 初始化状态转移矩阵
  A_.setZero();
  A_.block(0, 0, 3, 3) = I3;
  A_.block(0, 3, 3, 3) = I3 * dt_;
  A_.block(3, 3, 3, 3) = I3;
  A_.block(6, 6, 12, 12) = I12;

  // 初始化控制输入矩阵
  B_.setZero();
  B_.block(3, 0, 3, 3) = I3 * dt_;

  // 初始化测量矩阵
  C_.setZero();
  C_.block(0, 0, 3, 3) = -I3;
  C_.block(3, 0, 3, 3) = -I3;
  C_.block(6, 0, 3, 3) = -I3;
  C_.block(9, 0, 3, 3) = -I3;
  C_.block(12, 3, 3, 3) = -I3;
  C_.block(15, 3, 3, 3) = -I3;
  C_.block(18, 3, 3, 3) = -I3;
  C_.block(21, 3, 3, 3) = -I3;
  C_.block(0, 6, 12, 12) = I12;
  C_(24, 8) = 1;
  C_(25, 11) = 1;
  C_(26, 14) = 1;
  C_(27, 17) = 1;

  // 初始化协方差矩阵
  P_.setIdentity();
  P_ = large_variance_ * P_;

  // 初始化测量噪声矩阵 - 设置默认值
  RInit_ = Eigen::MatrixXd::Identity(28, 28);
  // 增加对足端位置测量的信任度
  for (int i = 0; i < 12; ++i) {
    RInit_(i, i) = 0.001;
  }
  // 增加对速度测量的信任度
  for (int i = 12; i < 24; ++i) {
    RInit_(i, i) = 0.01;
  }

  // 设置控制输入协方差矩阵的默认值
  Cu_ << 3.526318e-04, 6.698244e-05, 8.053905e-05, 6.698244e-05, 3.437584e-04,
      2.105601e-05, 8.053905e-05, 2.105601e-05, 4.948744e-04;

  // 初始化过程噪声矩阵
  QInit_ = Qdig_.asDiagonal();
  QInit_ += B_ * Cu_ * B_.transpose();

  // 初始化噪声检查 - 使用CovEst进行实际协方差估计
  RCheck_ =
      new CovEst(24, 1000);  // 24维测量噪声（去掉足部高度），等待1000个样本
  uCheck_ = new CovEst(3, 1000);    // 3维控制输入噪声，等待1000个样本
  accCheck_ = new CovEst(3, 1000);  // 3维加速度偏移量，等待1000个样本

  // 初始化速度滤波器
  vxFilter_ = new LPFilter(dt_, 3.0);
  vyFilter_ = new LPFilter(dt_, 3.0);
  vzFilter_ = new LPFilter(dt_, 3.0);

  // 初始化测量向量
  y_ = Eigen::VectorXd::Zero(28);
  yhat_ = Eigen::VectorXd::Zero(28);

  // 初始化滤波器变量
  Ppriori_ = Eigen::MatrixXd::Zero(18, 18);
  S_ = Eigen::MatrixXd::Zero(28, 28);
  Sy_ = Eigen::VectorXd::Zero(28);
  Sc_ = Eigen::MatrixXd::Zero(28, 18);
  SR_ = Eigen::MatrixXd::Zero(28, 28);
  STC_ = Eigen::MatrixXd::Zero(18, 28);
  IKC_ = Eigen::MatrixXd::Zero(18, 18);

  // 初始化足部变量
  feetH_.setZero();
  feetPos2Body_.setZero();
  feetVel2Body_.setZero();

  // 初始化协方差估计控制
  enable_covariance_estimation_ = false;  // 默认禁用
  covariance_counter_ = 0;

  // 尝试从YAML文件加载配置 - 这会覆盖上述默认值（如果配置文件存在的话）
  loadEstimatorConfig();

  // 输出初始化后的关键参数
  RCLCPP_INFO(logger_, "Estimator initialized with:");
  RCLCPP_INFO(logger_, "acc_offset: [%.6f, %.6f, %.6f]", acc_offset_(0),
              acc_offset_(1), acc_offset_(2));
  RCLCPP_INFO(logger_, "acc_scale: %.6f", acc_scale_);
}

void Estimator::loadEstimatorConfig() {
  std::string config_file = "estimator_config.yaml";

  try {
    if (!std::filesystem::exists(config_file)) {
      RCLCPP_INFO(logger_, "Config file %s not found. Using default values.",
                  config_file.c_str());
      RCLCPP_INFO(logger_,
                  "Run covariance estimation to generate calibration data.");
      return;
    }

    YAML::Node config = YAML::LoadFile(config_file);
    bool config_loaded = false;

    // 加载加速度偏移量
    if (config["acc_offset"]) {
      auto acc_data = config["acc_offset"];
      if (acc_data.size() == 3) {
        acc_offset_(0) = acc_data[0].as<double>();
        acc_offset_(1) = acc_data[1].as<double>();
        acc_offset_(2) = acc_data[2].as<double>();

        // 优先从配置文件中加载acc_scale，如果没有则计算
        if (config["acc_scale"]) {
          acc_scale_ = config["acc_scale"].as<double>();
          RCLCPP_INFO(logger_, "Loaded acc_scale from config: %.6f",
                      acc_scale_);
        } else {
          // 计算加速度scale因子: scale = ||acc_offset|| / ||g||
          // acc_offset已经包含了重力分量，直接计算模长比值
          acc_scale_ = acc_offset_.norm() / 9.81;
          RCLCPP_INFO(logger_, "Calculated acc_scale: %.6f", acc_scale_);
        }

        RCLCPP_INFO(logger_, "Loaded acc_offset: [%.6f, %.6f, %.6f]",
                    acc_offset_(0), acc_offset_(1), acc_offset_(2));
        config_loaded = true;
      }
    }

    // 加载控制输入协方差矩阵Cu_
    if (config["Cu_matrix"]) {
      auto cu_data = config["Cu_matrix"];
      if (cu_data.size() == 3) {
        for (int i = 0; i < 3; ++i) {
          if (cu_data[i].size() == 3) {
            for (int j = 0; j < 3; ++j) {
              Cu_(i, j) = cu_data[i][j].as<double>();
            }
          }
        }
        RCLCPP_INFO(logger_, "Loaded Cu_ matrix from configuration file");
        config_loaded = true;
      }
    }

    // 加载测量噪声协方差矩阵RInit_
    if (config["R_matrix"]) {
      auto r_data = config["R_matrix"];
      if (r_data.size() == 28) {
        for (int i = 0; i < 28; ++i) {
          if (r_data[i].size() == 28) {
            for (int j = 0; j < 28; ++j) {
              RInit_(i, j) = r_data[i][j].as<double>();
            }
          }
        }
        RCLCPP_INFO(logger_, "Loaded RInit_ matrix from configuration file");
        config_loaded = true;
      }
    }

    if (config_loaded) {
      RCLCPP_INFO(logger_, "Successfully loaded estimator config from %s",
                  config_file.c_str());
    } else {
      RCLCPP_WARN(
          logger_,
          "Config file exists but no valid data found. Using default values.");
    }
  } catch (const YAML::Exception &e) {
    RCLCPP_WARN(logger_, "Error loading config file %s: %s",
                config_file.c_str(), e.what());
  } catch (const std::exception &e) {
    RCLCPP_WARN(logger_, "Unexpected error loading config: %s", e.what());
  }
}

void Estimator::run() {
  // 处理足部相关数据
  feetH_.setZero();
  feetPosGlobalKine_ =
      robot_model_->getFeet2BPositions(*low_state_, FrameType::GLOBAL);
  feetVelGlobalKine_ =
      robot_model_->getFeet2BVelocities(*low_state_, FrameType::GLOBAL);

  // 设置系统噪声
  Q_ = QInit_;
  R_ = RInit_;

  // 根据足部接触状态调整系统噪声
  for (int i(0); i < 4; ++i) {
    if ((*contact_)(i) == 0) {
      // 未接触时增大方差
      Q_.block(6 + 3 * i, 6 + 3 * i, 3, 3) = large_variance_ * I3;
      R_.block(12 + 3 * i, 12 + 3 * i, 3, 3) = large_variance_ * I3;
      R_(24 + i, 24 + i) = large_variance_;
    } else {
      // 接触时根据相位平滑过渡
      // 调用windowFunc模板函数，提供所有必要参数
      trust_ = windowFunc<double>((*phase_)(i), 0.2);
      Q_.block(6 + 3 * i, 6 + 3 * i, 3, 3) =
          (1 + (1 - trust_) * large_variance_) *
          QInit_.block(6 + 3 * i, 6 + 3 * i, 3, 3);
      R_.block(12 + 3 * i, 12 + 3 * i, 3, 3) =
          (1 + (1 - trust_) * large_variance_) *
          RInit_.block(12 + 3 * i, 12 + 3 * i, 3, 3);
      R_(24 + i, 24 + i) =
          (1 + (1 - trust_) * large_variance_) * RInit_(24 + i, 24 + i);
    }
    // 存储足部位置和速度
    feetPos2Body_.segment(3 * i, 3) = feetPosGlobalKine_.col(i);
    feetVel2Body_.segment(3 * i, 3) = feetVelGlobalKine_.col(i);
  }

  // 获取当前姿态和加速度
  rotMatB2G_ = low_state_->getRotMat();
  // 计算控制输入：进行偏移校正
  u_ = rotMatB2G_ * (low_state_->imu.getAcc() - acc_offset_);

  // 状态预测 - 使用标准的卡尔曼滤波公式
  xhat_ = A_ * xhat_ + B_ * u_;
  yhat_ = C_ * xhat_;

  // 构建测量向量
  y_ << feetPos2Body_, feetVel2Body_, feetH_;

  // 卡尔曼滤波计算
  Ppriori_ = A_ * P_ * A_.transpose() + Q_;
  S_ = R_ + C_ * Ppriori_ * C_.transpose();
  Slu_ = S_.lu();
  Sy_ = Slu_.solve(y_ - yhat_);
  Sc_ = Slu_.solve(C_);
  SR_ = Slu_.solve(R_);
  STC_ = S_.transpose().lu().solve(C_);
  IKC_ = Eigen::MatrixXd::Identity(18, 18) - Ppriori_ * C_.transpose() * Sc_;

  // 状态更新
  xhat_ += Ppriori_ * C_.transpose() * Sy_;
  P_ = IKC_ * Ppriori_ * IKC_.transpose() +
       Ppriori_ * C_.transpose() * SR_ * STC_ * Ppriori_.transpose();

  // 速度滤波
  vxFilter_->addValue(xhat_(3) / acc_scale_);
  vyFilter_->addValue(xhat_(4) / acc_scale_);
  vzFilter_->addValue(xhat_(5) / acc_scale_);

  // 协方差估计 - 只有启用时才收集数据
  if (enable_covariance_estimation_) {
    // 为协方差估计构建24维测量向量（去掉足部高度部分）
    Eigen::VectorXd measurement_raw_24d(24);
    measurement_raw_24d << feetPos2Body_,
        feetVel2Body_;  // 只包含位置和速度，不包含高度

    Eigen::VectorXd control_input = u_;  // 控制输入

    RCheck_->addSample(measurement_raw_24d);  // 使用24维向量进行协方差估计
    uCheck_->addSample(control_input);
    accCheck_->addSample(low_state_->imu.getAcc());

    // 协方差估计结果输出
    covariance_counter_++;

    // 每200次迭代输出一次结果
    constexpr int OUTPUT_PERIOD = 200;
    if (covariance_counter_ % OUTPUT_PERIOD == 0) {
      // 使用固定的文件名，覆盖写入
      std::string filename = "estimator_config.yaml";

      try {
        YAML::Node config;

        // 获取当前时间用于时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now.time_since_epoch()) %
                  1000;

        // 添加元数据
        config["metadata"]["estimator_name"] = est_name_;
        config["metadata"]["output_number"] =
            covariance_counter_ / OUTPUT_PERIOD;
        config["metadata"]["total_iterations"] = covariance_counter_;

        // 格式化时间戳
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << "." << std::setfill('0') << std::setw(3) << ms.count();
        config["metadata"]["timestamp"] = ss.str();

        if (RCheck_->isReliable()) {
          // 获取完整的24维协方差矩阵和均值向量
          const auto &r_cov = RCheck_->getCovariance();
          // 保存R矩阵信息
          config["R_matrix_info"]["dimensions"] = "28x28";
          config["R_matrix_info"]["description"] =
              "Measurement noise cov matrix";
          config["R_matrix_info"]["valid_samples"] = RCheck_->getValidCount();
          config["R_matrix_info"]["total_samples"] = RCheck_->getTotalCount();

          double condition_number = RCheck_->getConditionNumber();
          config["R_matrix_info"]["condition_number"] = condition_number;
          config["R_matrix_info"]["matrix_status"] =
              std::isinf(condition_number) ? "SINGULAR" : "WELL-CONDITIONED";

          // 保存扩展到28x28的R矩阵
          std::vector<std::vector<double>> r_matrix_28x28(
              28, std::vector<double>(28, 0.0));
          for (int i = 0; i < 28; ++i) {
            for (int j = 0; j < 28; ++j) {
              if (i < 24 && j < 24) {
                r_matrix_28x28[i][j] = r_cov(i, j);
              } else if (i >= 24 && j >= 24 && i == j) {
                r_matrix_28x28[i][j] = 1.0;  // 足部高度对角线元素
              }
            }
          }
          config["R_matrix"] = r_matrix_28x28;

          RCLCPP_INFO(logger_,
                      "R matrix (28x28 measurement noise) saved to YAML");
        }

        if (uCheck_->isReliable()) {
          // 获取完整的3维控制输入协方差矩阵和均值向量
          const auto &u_cov = uCheck_->getCovariance();
          // 保存Cu矩阵信息
          config["Cu_matrix_info"]["dimensions"] = "3x3";
          config["Cu_matrix_info"]["description"] = "Control input cov matrix";
          config["Cu_matrix_info"]["valid_samples"] = uCheck_->getValidCount();
          config["Cu_matrix_info"]["total_samples"] = uCheck_->getTotalCount();
          config["Cu_matrix_info"]["condition_number"] =
              uCheck_->getConditionNumber();

          // 保存Cu矩阵
          std::vector<std::vector<double>> cu_matrix(3, std::vector<double>(3));
          for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
              cu_matrix[i][j] = u_cov(i, j);
            }
          }
          config["Cu_matrix"] = cu_matrix;

          RCLCPP_INFO(logger_,
                      "Cu matrix (3x3 control input noise) saved to YAML");
        }

        if (accCheck_->isReliable()) {
          // 获取加速度偏移量信息
          const auto &acc_mean = accCheck_->getMean();

          // 保存加速度偏移量
          std::vector<double> acc_offset_vector = {acc_mean(0), acc_mean(1),
                                                   acc_mean(2)};
          config["acc_offset"] = acc_offset_vector;
          config["acc_offset_info"]["description"] =
              "Acceleration bias mean vector";
          config["acc_offset_info"]["units"] =
              "m/s² for x, y, z acceleration components";
          config["acc_offset_info"]["valid_samples"] =
              accCheck_->getValidCount();
          config["acc_offset_info"]["total_samples"] =
              accCheck_->getTotalCount();

          // 计算并保存scale因子
          double calculated_scale = acc_mean.norm() / 9.81;
          config["acc_scale"] = calculated_scale;
          config["acc_scale_info"]["description"] = "Acceleration scale factor";
          config["acc_scale_info"]["calculated_value"] = calculated_scale;

          RCLCPP_INFO(logger_, "Acceleration offset saved to YAML");
          RCLCPP_INFO(logger_, "Calculated acceleration scale: %.6f",
                      calculated_scale);
        }

        // 写入YAML文件
        std::ofstream file(filename);
        if (!file.is_open()) {
          RCLCPP_ERROR(logger_, "Cannot create YAML output file: %s",
                       filename.c_str());
          return;
        }
        file << config;
        file.close();

        RCLCPP_INFO(logger_, "Complete estimator configuration saved to: %s",
                    filename.c_str());
      } catch (const std::exception &e) {
        RCLCPP_ERROR(logger_, "Error writing YAML configuration file: %s",
                     e.what());
      }
    }
  }
}

void Estimator::reset() {
  // 重置位置和速度部分的状态
  xhat_.segment(0, 6).setZero();
}

Vec3 Estimator::getPosition() { return xhat_.segment(0, 3) / acc_scale_; }

Vec3 Estimator::getVelocity() { return xhat_.segment(3, 3) / acc_scale_; }

Vec3 Estimator::getFootPos(int i) {
  return getPosition() +
         low_state_->getRotMat() *
             robot_model_->getFootPosition(*low_state_, i, FrameType::BODY);
}

Vec34 Estimator::getFeetPos() {
  Vec34 feetPos;
  for (int i(0); i < 4; ++i) {
    feetPos.col(i) = getFootPos(i);
  }
  return feetPos;
}

Vec34 Estimator::getFeetVel() {
  Vec34 feetVel =
      robot_model_->getFeet2BVelocities(*low_state_, FrameType::GLOBAL);
  for (int i(0); i < 4; ++i) {
    feetVel.col(i) += getVelocity();
  }
  return feetVel;
}

Vec34 Estimator::getPosFeet2BGlobal() {
  Vec34 feet2BPos;
  for (int i(0); i < 4; ++i) {
    feet2BPos.col(i) = getFootPos(i) - getPosition();
  }
  return feet2BPos;
}

void Estimator::setDt(double dt) {
  dt_ = dt;

  // 更新状态转移矩阵A_中依赖于dt的部分
  A_.block(0, 3, 3, 3) = I3 * dt_;

  // 更新控制输入矩阵B_中依赖于dt的部分
  B_.block(3, 0, 3, 3) = I3 * dt_;

  // 更新过程噪声矩阵QInit_
  QInit_ = Qdig_.asDiagonal();
  QInit_ += B_ * Cu_ * B_.transpose();

  // 更新速度滤波器的dt参数
  if (vxFilter_) {
    vxFilter_->setDt(dt_);
  }
  if (vyFilter_) {
    vyFilter_->setDt(dt_);
  }
  if (vzFilter_) {
    vzFilter_->setDt(dt_);
  }
}

Go2Estimator::Go2Estimator(LowlevelState *low_state, VecInt4 *contact,
                           Vec4 *phase, double dt, rclcpp::Logger logger)
    : Estimator(new Go2Robot(), low_state, contact, phase, dt, logger) {
  // Go2特定的初始化 - 如果需要的话
}

Vita00Estimator::Vita00Estimator(LowlevelState *low_state, VecInt4 *contact,
                                 Vec4 *phase, double dt, rclcpp::Logger logger)
    : Estimator(new Vita00Robot(), low_state, contact, phase, dt, logger) {
  // Vita00特定的初始化 - 如果需要的话
}

Vita01Estimator::Vita01Estimator(LowlevelState *low_state, VecInt4 *contact,
                                 Vec4 *phase, double dt, rclcpp::Logger logger)
    : Estimator(new Vita01Robot(), low_state, contact, phase, dt, logger) {
  // Vita01特定的初始化 - 如果需要的话
}

Vita01bEstimator::Vita01bEstimator(LowlevelState *low_state, VecInt4 *contact,
  Vec4 *phase, double dt, rclcpp::Logger logger)
: Estimator(new Vita01bRobot(), low_state, contact, phase, dt, logger) {
// Vita01b特定的初始化 - 如果需要的话
}

}  // namespace vita_odom