// Copyright 2025 VitaDynamics Limited

#include "vita_odom/imu_odometry.hpp"

#include <array>
#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <utility>

// ROS2
#include <tf2/LinearMath/Quaternion.h>

#include <Eigen/Dense>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include "vita_odom/feet_contact_forces.hpp"
#include "vita_odom/system_utils.hpp"
#include "vita_odom/unit_calc.hpp"
#include "vita_odom/unitree_estimator.hpp"

constexpr double default_dt = 1.0 / 200.0;

namespace vita_odom {

ImuOdometry::ImuOdometry() : Node("imu_odometry") {
  // Set CPU affinity to core 0 for better real-time performance
  if (!system_utils::SetCpuAffinity(0)) {
    RCLCPP_WARN(this->get_logger(), "Failed to set CPU affinity to core 0");
  } else {
    RCLCPP_INFO(this->get_logger(), "Successfully bound to CPU core 0");
  }

  // 声明和获取参数
  declare_parameter("world_frame_id", "unitree_odom");
  declare_parameter("base_frame_id", "base_link_imu");
  declare_parameter("enable_covariance_estimation", false);

  world_frame_id_ = get_parameter("world_frame_id").as_string();
  base_frame_id_ = get_parameter("base_frame_id").as_string();
  enable_covariance_estimation_ =
      get_parameter("enable_covariance_estimation").as_bool();

  RCLCPP_INFO(this->get_logger(), "Covariance estimation: %s",
              enable_covariance_estimation_ ? "enabled" : "disabled");

  // 初始化协方差数组 - 直接从unitree_guide复用
  pose_covariance_ = {1e-9, 0, 0,   0, 0,   0, 0, 1e-3, 1e-9, 0,   0, 0,
                      0,    0, 1e6, 0, 0,   0, 0, 0,    0,    1e6, 0, 0,
                      0,    0, 0,   0, 1e6, 0, 0, 0,    0,    0,   0, 1e-9};

  twist_covariance_ = {1e-9, 0, 0,   0, 0,   0, 0, 1e-3, 1e-9, 0,   0, 0,
                       0,    0, 1e6, 0, 0,   0, 0, 0,    0,    1e6, 0, 0,
                       0,    0, 0,   0, 1e6, 0, 0, 0,    0,    0,   0, 1e-9};

  // 初始化ROS2相关
  tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(this);

  // 创建发布者和订阅者
  rclcpp::QoS qos_profile(5);
  // qos_profile.history(rclcpp::HistoryPolicy::KeepAll);

  odom_pub_ =
      create_publisher<nav_msgs::msg::Odometry>("/rt/odom", qos_profile);

#ifdef VITA_MSG
  imu_debug_pub_ = create_publisher<lowlevel_msg::msg::IMUDebug>(
      "/rt/imu_debug", qos_profile);
  low_state_sub_ = create_subscription<LowStateMsg>(
      "/rt/lowstate", qos_profile,
      std::bind(&ImuOdometry::LowStateCallback, this, std::placeholders::_1));
#else
  low_state_sub_ = create_subscription<LowStateMsg>(
      "/lowstate", qos_profile,
      std::bind(&ImuOdometry::LowStateCallback, this, std::placeholders::_1));
#endif

// 初始化Unitree状态估计器相关
#ifdef VITA_MSG
  robot_model_ = std::make_unique<Vita01bRobot>();
#else
  robot_model_ = std::make_unique<Go2Robot>();
#endif
  low_state_ = std::make_unique<LowlevelState>();
  contact_state_.setZero();
  phase_.setZero();

  // 创建状态估计器 - 使用默认dt，后续会根据实际消息时间间隔动态更新
#ifdef VITA_MSG
  estimator_ = std::make_unique<Vita01bEstimator>(
      low_state_.get(), &contact_state_, &phase_, default_dt);
#else
  estimator_ =
      std::make_unique<Go2Estimator>(low_state_.get(), &contact_state_, &phase_,
                                     default_dt, this->get_logger());
#endif

  // 设置协方差估计参数
  estimator_->setEnableCovarianceEstimation(enable_covariance_estimation_);
  if (enable_covariance_estimation_) {
    RCLCPP_INFO(
        this->get_logger(),
        "Covariance estimation enabled, will collect data in state estimator");
  }

  // 创建足部接触力计算器
  feet_contact_forces_ =
      std::make_unique<FeetContactForces>(robot_model_.get());

  // 初始化标志和初始姿态
  is_first_imu_data_ = true;
  initial_quat_.setIdentity();
  last_msg_time_ = rclcpp::Time(0, 0, RCL_ROS_TIME);
}

ImuOdometry::~ImuOdometry() {}

void ImuOdometry::LowStateCallback(const typename LowStateMsg::SharedPtr msg) {
  rclcpp::Time current_time = this->get_clock()->now();

  // 计算dt用于状态估计器
  double dt = 0.0;
  if (last_msg_time_.nanoseconds() != 0) {
    dt = (current_time - last_msg_time_).seconds();
    // 限制dt在合理范围内，避免异常值
    if (dt > 0.1 || dt <= 0.0) {
      RCLCPP_WARN(this->get_logger(), "Abnormal dt: %f, using default", dt);
      dt = default_dt;
    }
  } else {
    dt = default_dt;  // 第一次回调使用默认值
  }
  last_msg_time_ = current_time;

  // 更新状态估计器的dt
  estimator_->setDt(dt);

  // 处理IMU数据
  const auto &imu_state = msg->imu_state;
  double quaternion[4] = {imu_state.quaternion[0], imu_state.quaternion[1],
                          imu_state.quaternion[2], imu_state.quaternion[3]};

#ifdef VITA_MSG
  double gyroscope[3] = {DegPerSecToRadPerSec(imu_state.gyroscope[0]),
                         DegPerSecToRadPerSec(imu_state.gyroscope[1]),
                         DegPerSecToRadPerSec(imu_state.gyroscope[2])};
#else
  double gyroscope[3] = {imu_state.gyroscope[0], imu_state.gyroscope[1],
                         imu_state.gyroscope[2]};
#endif

#ifdef VITA_MSG
  double accelerometer[3] = {
      GToMetersPerSecSquared(imu_state.accelerometer[0]),
      GToMetersPerSecSquared(imu_state.accelerometer[1]),
      GToMetersPerSecSquared(imu_state.accelerometer[2])};
#else
  double accelerometer[3] = {imu_state.accelerometer[0],
                             imu_state.accelerometer[1],
                             imu_state.accelerometer[2]};
#endif

  // 如果是第一帧IMU数据，记录初始姿态并重置估计器
  if (is_first_imu_data_) {
    initial_quat_ = Eigen::Quaterniond(quaternion[0], quaternion[1],
                                       quaternion[2], quaternion[3]);
    estimator_->reset();
    is_first_imu_data_ = false;
    RCLCPP_INFO(this->get_logger(),
                "Initial pose set as origin, estimator has been reset.");
  }

  low_state_->UpdateIMU(quaternion, gyroscope, accelerometer);
  low_state_->UpdateJointState(msg->motor_state);
#ifdef VITA_MSG
  low_state_->UpdateTick(msg->state_id);
#else
  low_state_->UpdateTick(msg->tick);
#endif

  // 更新足部接触状态
#ifdef VITA_MSG
  CalcFootContactState();
#else
  UpdateFootContactState(msg->foot_force);
#endif

  // 运行状态估计
  estimator_->run();

  // 发布里程计数据
  PublishOdometry(current_time);
}

void ImuOdometry::CalcFootContactState() {
  Vec34 feet_grf;
  const int success_count =
      feet_contact_forces_->getAllFeetGRF(*low_state_, feet_grf);

  assert(success_count == 4);
  constexpr double grf_threshold_max = 40.0;
  constexpr double grf_threshold_min = 30.0;

#ifdef VITA_MSG
  lowlevel_msg::msg::IMUDebug imu_debug_msg;
#endif
  for (int i = 0; i < 4; i++) {
    double vertical_force = feet_grf.col(i)(2);  // Z轴分量（垂直力）
#ifdef VITA_MSG
    imu_debug_msg.foot_force_calc[i] = vertical_force;
#endif
    static bool prev_contact[4] = {false, false, false, false};
    // 基于滞后比较器更新接触状态
    if (vertical_force > grf_threshold_max) {
      prev_contact[i] = true;
    } else if (vertical_force < grf_threshold_min) {
      prev_contact[i] = false;
    }
    contact_state_(i) = prev_contact[i] ? 1 : 0;
    phase_(i) = contact_state_(i) ? 0.0 : 0.5;
  }

#ifdef VITA_MSG
  imu_debug_pub_->publish(imu_debug_msg);
#endif
}

void ImuOdometry::UpdateFootContactState(
    const std::array<int16_t, 4> &foot_forces) {
  // 首先，使用原始传感器值计算接触状态（作为备份）
  const int16_t force_threshold_high = 25;  // 接触阈值上限
  const int16_t force_threshold_low = 10;   // 接触阈值下限

  // 状态持久变量
  static bool prev_contact[4] = {false, false, false, false};

  // 基于滞后比较器更新接触状态
  for (int i = 0; i < 4; i++) {
    if (foot_forces[i] > force_threshold_high) {
      prev_contact[i] = true;
    } else if (foot_forces[i] < force_threshold_low) {
      prev_contact[i] = false;
    }
    contact_state_(i) = prev_contact[i] ? 1 : 0;
  }

  for (int i = 0; i < 4; i++) {
    phase_(i) = contact_state_(i) ? 0.0 : 0.5;
  }
}

void ImuOdometry::PublishOdometry(const rclcpp::Time &current_time) {
  // 获取状态估计器的结果
  Vec3 position = estimator_->getPosition();
  Vec3 velocity = estimator_->getVelocity();
  Eigen::Quaterniond orientation(
      low_state_->imu.quaternion[0], low_state_->imu.quaternion[1],
      low_state_->imu.quaternion[2], low_state_->imu.quaternion[3]);

  // 完整初始姿态矫正
  Eigen::Quaterniond quat_correction = initial_quat_.inverse();
  Eigen::Quaterniond transformed_orientation = quat_correction * orientation;
  Vec3 transformed_position = quat_correction * position;

  // 填充TF数据
  odom_tf_.header.stamp = current_time;
  odom_tf_.header.frame_id = world_frame_id_;
  odom_tf_.child_frame_id = base_frame_id_;

  odom_tf_.transform.translation.x = transformed_position(0);
  odom_tf_.transform.translation.y = transformed_position(1);
  odom_tf_.transform.translation.z = transformed_position(2);

  odom_tf_.transform.rotation.w = transformed_orientation.w();
  odom_tf_.transform.rotation.x = transformed_orientation.x();
  odom_tf_.transform.rotation.y = transformed_orientation.y();
  odom_tf_.transform.rotation.z = transformed_orientation.z();

  // 发布TF
  tf_broadcaster_->sendTransform(odom_tf_);

  // 填充里程计消息
  odom_msg_.header.stamp = current_time;
  odom_msg_.header.frame_id = world_frame_id_;
  odom_msg_.child_frame_id = base_frame_id_;

  // 设置位姿
  odom_msg_.pose.pose.position.x = transformed_position(0);
  odom_msg_.pose.pose.position.y = transformed_position(1);
  odom_msg_.pose.pose.position.z = transformed_position(2);

  odom_msg_.pose.pose.orientation.w = transformed_orientation.w();
  odom_msg_.pose.pose.orientation.x = transformed_orientation.x();
  odom_msg_.pose.pose.orientation.y = transformed_orientation.y();
  odom_msg_.pose.pose.orientation.z = transformed_orientation.z();

  // 设置速度
  // 将全局坐标系下的速度转换到机器人坐标系
  Mat3 R = low_state_->getRotMat();
  Vec3 vel_body = R.transpose() * velocity;

  odom_msg_.twist.twist.linear.x = vel_body(0);
  odom_msg_.twist.twist.linear.y = vel_body(1);
  odom_msg_.twist.twist.linear.z = vel_body(2);

  odom_msg_.twist.twist.angular.x = low_state_->imu.gyroscope[0];
  odom_msg_.twist.twist.angular.y = low_state_->imu.gyroscope[1];
  odom_msg_.twist.twist.angular.z = low_state_->imu.gyroscope[2];

  // 设置协方差
  for (size_t i = 0; i < 36; ++i) {
    odom_msg_.pose.covariance[i] = pose_covariance_[i];
    odom_msg_.twist.covariance[i] = twist_covariance_[i];
  }

  // 发布里程计消息
  odom_pub_->publish(odom_msg_);
}

}  // namespace vita_odom
