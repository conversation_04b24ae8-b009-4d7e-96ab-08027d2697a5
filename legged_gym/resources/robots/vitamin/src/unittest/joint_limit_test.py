import numpy as np
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd
import time
import threading

# 初始状态的关节位置（从linear_interpolation_vita01.py复制）
initial_joint_pos = np.array(
    [-0.161, 1.19, -2.76, 0.161, 1.19, -2.76, -0.161, 1.19, -2.76, 0.161, 1.19, -2.76],
    dtype=float,
)

# 站立姿态的关节位置 (FR, FL, RR, RL)
standing_pos = np.array(
    [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5], dtype=float
)

# 关节极限测试序列
# 基于XML文件中的关节限制：
# 髋关节限制：
# 右侧髋关节 (FR, RR): -0.4538 到 0.6632
# 左侧髋关节 (FL, RL): -0.6632 到 0.4538
# 大腿关节限制：
# 前腿大腿关节 (FR, FL): -0.87266 到 3.14159
# 后腿大腿关节 (RR, RL): -0.87266 到 4.1713
# 小腿关节限制：
# 所有小腿关节 (FR, FL, RR, RL): -2.757 到 -0.89
# 简化的测试序列：每个动作都需要用户确认，每个极限动作后跟着一个标准站立
# 髋关节测试序列
hip_test_positions = [
    # 1. 从初始位置到站立位置
    {
        "name": "Stand up from initial position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 2. FR髋关节向内收缩 (负方向极限) —— -0.25
    {
        "name": "FR hip inward limit",
        "pos": np.array(
            [-0.25, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 3. FR髋关节向外展开 (正方向极限) —— 0.3
    {
        "name": "FR hip outward limit",
        "pos": np.array(
            [0.3, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 4. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 5. FL髋关节向内收缩 (负方向极限) —— -0.3
    {
        "name": "FL hip inward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, -0.3, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 6. FL髋关节向外展开 (正方向极限) —— 0.22
    {
        "name": "FL hip outward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.22, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 7. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 8. RR髋关节向内收缩 (负方向极限) —— -0.25
    {
        "name": "RR hip inward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.25, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 9. RR髋关节向外展开 (正方向极限) —— 0.5
    {
        "name": "RR hip outward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, 0.5, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 10. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 11. RL髋关节向内收缩 (负方向极限) —— -0.5
    {
        "name": "RL hip inward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, -0.5, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 12. RL髋关节向外展开 (正方向极限) —— 0.25
    {
        "name": "RL hip outward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.25, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 13. 最终站立姿态
    {
        "name": "Final standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
]

# 大腿关节测试序列
thigh_test_positions = [
    # 1. 从站立位置开始
    {
        "name": "Start from standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 2. FR大腿关节向后伸展 (负方向极限) —— 0.5
    {
        "name": "FR thigh backward limit",
        "pos": np.array(
            [-0.04, 0.5, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 3. FR大腿关节向前弯曲 (正方向极限) —— 1.2
    {
        "name": "FR thigh forward limit",
        "pos": np.array(
            [-0.04, 1.2, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 4. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 5. FL大腿关节向后伸展 (负方向极限) —— 0.5
    {
        "name": "FL thigh backward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.5, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 6. FL大腿关节向前弯曲 (正方向极限) —— 1.2
    {
        "name": "FL thigh forward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 1.2, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 7. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 8. RR大腿关节向后伸展 (负方向极限) —— 0.5
    {
        "name": "RR thigh backward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.5, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 9. RR大腿关节向前弯曲 (正方向极限) —— 1.2
    {
        "name": "RR thigh forward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 1.2, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 10. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 11. RL大腿关节向后伸展 (负方向极限) —— 0.5
    {
        "name": "RL thigh backward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.5, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 12. RL大腿关节向前弯曲 (正方向极限) —— 1.2
    {
        "name": "RL thigh forward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 1.2, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 13. 最终站立姿态
    {
        "name": "Final standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
]

# 小腿关节测试序列
calf_test_positions = [
    # 1. 从站立位置开始
    {
        "name": "Start from standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 2. FR小腿关节向上收缩 (负方向极限) —— -2.1
    {
        "name": "FR calf upward limit",
        "pos": np.array(
            [-0.04, 0.8, -2.1, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 3. FR小腿关节向下伸展 (正方向极限) —— -1.1
    {
        "name": "FR calf downward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.1, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 4. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 5. FL小腿关节向上收缩 (负方向极限) —— -2.0
    {
        "name": "FL calf upward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -2.0, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 6. FL小腿关节向下伸展 (正方向极限) —— -1.1
    {
        "name": "FL calf downward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.1, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 7. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 8. RR小腿关节向上收缩 (负方向极限) —— -1.7
    {
        "name": "RR calf upward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.7, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 9. RR小腿关节向下伸展 (正方向极限) —— -1.1
    {
        "name": "RR calf downward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.1, 0.04, 0.8, -1.5],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 10. 恢复站立
    {
        "name": "Return to standing",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
    # 11. RL小腿关节向上收缩 (负方向极限) —— -1.6
    {
        "name": "RL calf upward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.6],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 12. RL小腿关节向下伸展 (正方向极限) —— -1.1
    {
        "name": "RL calf downward limit",
        "pos": np.array(
            [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.1],
            dtype=float,
        ),
        "duration": 2.0,
    },
    # 13. 最终站立姿态
    {
        "name": "Final standing position",
        "pos": standing_pos.copy(),
        "duration": 2.0,
    },
]

# 合并所有测试序列，按顺序执行：髋关节 -> 大腿 -> 小腿
all_test_positions = []

# 添加髋关节测试
for test in hip_test_positions:
    test_copy = test.copy()
    test_copy["name"] = f"[Hip] {test['name']}"
    all_test_positions.append(test_copy)

# 添加大腿关节测试
for test in thigh_test_positions:
    test_copy = test.copy()
    test_copy["name"] = f"[Thigh] {test['name']}"
    all_test_positions.append(test_copy)

# 添加小腿关节测试
for test in calf_test_positions:
    test_copy = test.copy()
    test_copy["name"] = f"[Calf] {test['name']}"
    all_test_positions.append(test_copy)

# 控制参数
dt = 0.002  # 2ms发送一次


class JointLimitTest(Node):
    def __init__(self, mode="SIM"):
        super().__init__("joint_limit_test")

        # 设置运行模式
        self.mode = mode.upper()
        if self.mode not in ["SIM", "REAL"]:
            raise ValueError(f"Invalid mode: {mode}. Must be 'SIM' or 'REAL'")

        # 使用合并后的测试序列
        self.test_positions = all_test_positions
        self.test_name = f"Complete Joint Limit Test ({self.mode} Mode)"

        # 根据模式选择topic
        topic = "/sim/lowcmd" if self.mode == "SIM" else "/rl_lowcmd"
        self.pub = self.create_publisher(LowCmd, topic, 1)
        self.timer = self.create_timer(dt, self.timer_callback)

        # 初始化命令消息
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF

        # 初始化电机命令（从初始位置开始）
        for i in range(12):
            motor_cmd = MotorCmd()
            motor_cmd.mode = 0x01
            motor_cmd.q = initial_joint_pos[i]
            motor_cmd.kp = 0.0
            motor_cmd.dq = 0.0
            motor_cmd.kd = 0.0
            motor_cmd.tau = 0.0
            self.cmd.motor_cmd.append(motor_cmd)

        # 测试状态
        self.current_test_index = 0
        self.current_pos = initial_joint_pos.copy()
        self.target_pos = initial_joint_pos.copy()
        self.start_pos = initial_joint_pos.copy()
        self.step_count = 0
        self.total_steps = 0
        self.finished = False

        self.get_logger().info(f"{self.test_name} initialized")
        self.get_logger().info(f"Total tests: {len(self.test_positions)}")

    def start_next_test(self):
        if self.current_test_index >= len(self.test_positions):
            self.get_logger().info("All joint limit tests completed!")
            self.finished = True
            threading.Thread(target=self._shutdown).start()
            return

        test = self.test_positions[self.current_test_index]
        self.start_pos = self.current_pos.copy()
        self.target_pos = test["pos"].copy()
        self.total_steps = int(test["duration"] / dt)
        self.step_count = 0

        self.get_logger().info(
            f"Starting test {self.current_test_index + 1}: {test['name']}"
        )
        self.get_logger().info(
            f"Duration: {test['duration']}s ({self.total_steps} steps)"
        )

    def timer_callback(self):
        if self.finished:
            return

        # 如果是第一次运行或当前测试完成，开始下一个测试
        if self.step_count == 0 and self.total_steps == 0:
            self.start_next_test()
            if self.finished:
                return

        if self.step_count < self.total_steps:
            # 计算当前步骤的插值比例
            ratio = self.step_count / self.total_steps

            # 线性插值计算当前关节位置
            self.current_pos = (1 - ratio) * self.start_pos + ratio * self.target_pos

            # 更新电机命令
            for i in range(12):
                self.cmd.motor_cmd[i].q = self.current_pos[i]
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].tau = 0.0

                # 根据模式和关节类型设置kp/kd参数
                # 小腿关节索引：2, 5, 8, 11 (每组的第3个关节)
                is_calf_joint = i % 3 == 2

                if self.mode == "REAL" and is_calf_joint:
                    # REAL模式下小腿关节的kp/kd要除以4（减速比原因）
                    self.cmd.motor_cmd[i].kp = 30.0 / 4.0
                    self.cmd.motor_cmd[i].kd = 1.0 / 4.0
                else:
                    # SIM模式或非小腿关节使用标准参数
                    self.cmd.motor_cmd[i].kp = 30.0
                    self.cmd.motor_cmd[i].kd = 1.0

            # 发布命令
            self.pub.publish(self.cmd)

            # 更新步骤计数
            self.step_count += 1

            # 每500步打印一次进度
            if self.step_count % 500 == 0:
                progress = (self.step_count / self.total_steps) * 100
                self.get_logger().info(
                    f"Step {self.current_test_index + 1} progress: {progress:.1f}%"
                )

        else:
            # 当前步骤完成
            test = self.test_positions[self.current_test_index]
            test_name = test["name"]
            self.current_pos = self.target_pos.copy()
            self.get_logger().info(f"Completed: {test_name}")

            # 自动继续下一个步骤
            self.current_test_index += 1
            self.step_count = 0
            self.total_steps = 0

    def _shutdown(self):
        time.sleep(1.0)
        rclpy.shutdown()


def main(args=None):
    print("Complete Joint Limit Test")
    print("=========================")
    print("Available modes:")
    print("  1. SIM mode (Simulation)")
    print("  2. REAL mode (Real robot)")
    print()

    while True:
        try:
            choice = input("Please select mode (1 or 2): ").strip()
            if choice == "1":
                mode = "SIM"
                break
            elif choice == "2":
                mode = "REAL"
                break
            else:
                print("Invalid choice. Please enter 1 or 2.")
        except KeyboardInterrupt:
            print("\nTest cancelled.")
            return

    print(f"\n{mode} Mode Selected")
    print("=" * (len(mode) + 13))
    print("This test will systematically test the range of motion for all joints.")
    print("Test sequence: Hip joints → Thigh joints → Calf joints")

    if mode == "SIM":
        print("Topic: /sim/lowcmd")
        print("Parameters: Standard kp/kd for all joints")
    else:
        print("Topic: /rl_lowcmd")
        print("Parameters: Reduced kp/kd for calf joints (÷4 for gear ratio)")

    print("\nThe robot will automatically move through the following sequence:")
    print()
    for i, test in enumerate(all_test_positions):
        print(f"  {i+1}. {test['name']} ({test['duration']}s)")
    print()
    print("The test will run automatically without user confirmation.")
    print("Press Ctrl+C to stop the test at any time.")
    print("\nPress Enter to start the complete test sequence...")
    input()

    rclpy.init(args=args)
    node = JointLimitTest(mode)

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    finally:
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == "__main__":
    main()
