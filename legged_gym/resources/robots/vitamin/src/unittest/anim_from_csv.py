import numpy as np
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd
import pandas as pd
import time
import threading
import matplotlib.pyplot as plt
JNT_NUM = 14
class CSVPlaybackControl(Node):
    def __init__(self, csv_file):
        super().__init__('csv_playback_control')
        
        # 仿真环境的初始姿态 (单位:弧度)
        self.sim_initial_pos = np.array(
            [-0.161, 1.19, -2.76, 0.161, 1.19, -2.76, -0.161, 1.19, -2.76, 0.161, 1.19, -2.76, 0, 0],
            dtype=float
        )
        
        self.target_joint_pos = np.array(
            [0.0,0.76,-1.43,0.0,0.76,-1.43,0.0,0.76,-1.43,0.0,0.76,-1.43, 0, 0], dtype=float
        )
        # 动画数据的初始姿态 (单位:度转换为弧度)
        self.anim_initial_pos = np.array(
            [0.0,0.76,-1.43,0.0,0.76,-1.43,0.0,0.76,-1.43,0.0,0.76,-1.43, 0, 0],
            dtype=float
        )

        # 加载CSV文件
        self.df = pd.read_csv(csv_file)

        self.get_logger().info(f"Loaded CSV with {len(self.df)} frames")
        
        # 创建发布者
        self.pub = self.create_publisher(LowCmd, "/sim/lowcmd", 1)
        
        # 控制参数 - 全局使用0.002秒间隔
        self.dt = 0.002  # 2ms用于所有阶段
        
        # 初始化定时器
        self.timer = self.create_timer(self.dt, self.timer_callback)
        
        # 初始化命令消息
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF
        
        # 初始化电机命令(JNT_NUM个关节)
        for i in range(JNT_NUM):
            motor_cmd = MotorCmd()
            motor_cmd.mode = 0x01  # 位置模式
            motor_cmd.q = self.sim_initial_pos[i]
            motor_cmd.kp = 30.0    # 位置增益
            motor_cmd.dq = 0.0
            motor_cmd.kd = 1.0     # 速度增益
            motor_cmd.tau = 0.0
            self.cmd.motor_cmd.append(motor_cmd)
        
        # 关节名称到索引的映射
        self.joint_mapping = {
            'FR_Hip': 0,
            'FR_Thigh': 1,
            'FR_Calf': 2,
            'FL_Hip': 3,
            'FL_Thigh': 4,
            'FL_Calf': 5,
            'RR_Hip': 6,
            'RR_Thigh': 7,
            'RR_Calf': 8,
            'RL_Hip': 9,
            'RL_Thigh': 10,
            'RL_Calf': 11,
            'Head_X': 12,
            'Head_Z': 13,
        }
        
        # 控制变量
        self.current_frame = 0
        self.finished = False
        self.state = "transition_to_initial"  # 状态机: transition_to_initial, playing, done
        self.transition_steps = 1000  # 过渡到初始姿态的步数 (1000步 * 0.002秒 = 2秒)
        self.current_transition_step = 0
        
        # 存储动画初始姿态的副本,用于播放阶段的偏移计算
        self.anim_base_pos = self.anim_initial_pos.copy()
        
        # 播放阶段相关变量
        self.play_step = 0
        self.play_steps_per_frame = 500/25
        self.play_current_frame = 0
        self.play_next_frame = 0
        self.play_current_pos = None
        self.play_next_pos = None
        
        self.get_logger().info("CSV播放控制节点已初始化")

    def plot_joint_angles(self):
        """绘制CSV中每个关节的角度曲线图"""
        plt.figure(figsize=(15, JNT_NUM))
        
        # 创建JNT_NUM个子图
        for i, joint_name in enumerate(self.joint_mapping.keys()):
            plt.subplot(5, 3, i+1)  # 4行3列布局
            
            # 获取关节数据
            joint_data = self.df[joint_name]
            
            # 绘制曲线
            plt.plot(joint_data, label=joint_name)
            
            # 设置标题和标签
            plt.title(joint_name)
            plt.xlabel('Frame')
            plt.ylabel('Angle (degrees)')
            plt.grid(True)
            
            # 自动调整刻度
            plt.tight_layout()
        
        # 保存图像
        fig_path="/home/<USER>/Documents/test_output/joint_angles_plot.png"
        plt.savefig(fig_path)
        print(f"Joint angles plot saved to:{fig_path}")
        plt.close()
        self.get_logger().info("关节角度曲线图已保存到文件")

    def timer_callback(self):
        if self.finished:
            return
            
        if self.state == "transition_to_initial":
            # 计算过渡比例
            ratio = self.current_transition_step / self.transition_steps
            
            # 线性插值计算当前关节位置
            current_joint_pos = (1 - ratio) * self.sim_initial_pos + ratio * self.target_joint_pos
            
            # 更新电机命令
            for i in range(JNT_NUM):
                self.cmd.motor_cmd[i].q = current_joint_pos[i]
                self.cmd.motor_cmd[i].kp = 30.0  # 位置增益
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 1.0  # 速度增益
                self.cmd.motor_cmd[i].tau = 0.0
            
            # 发布命令
            self.pub.publish(self.cmd)
            
            # 更新步骤计数
            self.current_transition_step += 1
            
            if self.current_transition_step % 100 == 0:
                self.get_logger().info(f"过渡到初始姿态: {self.current_transition_step}/{self.transition_steps}")
            
            # 检查是否完成过渡
            if self.current_transition_step >= self.transition_steps:
                self.get_logger().info("已过渡到动画初始姿态,开始播放CSV数据")
                self.state = "playing"
                # 初始化播放阶段
                self._init_playback()
                
        elif self.state == "playing":
            # 计算当前步在帧内的比例
            ratio = self.play_step / self.play_steps_per_frame
            
            # 线性插值计算当前关节位置
            current_joint_pos = (1 - ratio) * self.play_current_pos + ratio * self.play_next_pos
            
            # 更新电机命令
            for i in range(JNT_NUM):
                self.cmd.motor_cmd[i].q = current_joint_pos[i]
                self.cmd.motor_cmd[i].kp = 30.0  # 位置增益
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 1.0  # 速度增益
                self.cmd.motor_cmd[i].tau = 0.0
            
            # 发布命令
            self.pub.publish(self.cmd)
            
            # 更新步计数
            self.play_step += 1
            
            # 检查是否需要切换到下一帧
            if self.play_step >= self.play_steps_per_frame:
                # 重置步计数
                self.play_step = 0
                
                # 移动到下一帧
                self.play_current_frame = self.play_next_frame
                self.play_next_frame += 1
                
                # 更新当前帧位置
                self.play_current_pos = self.play_next_pos.copy()
                
                # 检查是否还有下一帧
                if self.play_next_frame < len(self.df):
                    # 获取下一帧数据
                    frame_data = self.df.iloc[self.play_next_frame]
                    
                    # 计算下一帧的目标位置
                    next_pos = np.zeros(JNT_NUM)
                    for joint_name, joint_idx in self.joint_mapping.items():
                        # 读取CSV中的角度偏移量（度）
                        degrees_offset = frame_data[joint_name]
                        
                        # 转换为弧度偏移量
                        # radians_offset = np.deg2rad(degrees_offset)
                        radians_offset = degrees_offset
                        
                        # 计算目标位置：动画初始姿态 + 偏移量
                        next_pos[joint_idx] = self.anim_base_pos[joint_idx] + radians_offset
                    
                    self.play_next_pos = next_pos
                    
                    # 每10帧记录一次进度
                    if self.play_next_frame % 10 == 0:
                        self.get_logger().info(f"播放进度: {self.play_next_frame}/{len(self.df)}")
                else:
                    # 播放完成
                    self.get_logger().info("CSV播放完成")
                    self.state = "done"
                    # 等待最后一个命令发送完成
                    time.sleep(0.5)
                    # 标记为已完成
                    self.finished = True
                    # 创建一个单独的线程来关闭ROS
                    threading.Thread(target=self._shutdown).start()

    def _init_playback(self):
        """初始化播放阶段"""
        self.play_step = 0
        self.play_current_frame = 0
        self.play_next_frame = 1
        
        # 计算第0帧的目标位置
        self.play_current_pos = self.target_joint_pos.copy()
        # frame_data_0 = self.df.iloc[0]
        # for joint_name, joint_idx in self.joint_mapping.items():
        #     degrees_offset = frame_data_0[joint_name]
        #     radians_offset = np.deg2rad(degrees_offset)
        #     self.play_current_pos[joint_idx] = self.anim_base_pos[joint_idx] + radians_offset
        
        # 计算第1帧的目标位置（如果存在）
        if len(self.df) > 1:
            frame_data_1 = self.df.iloc[1]
            self.play_next_pos = np.zeros(JNT_NUM)
            for joint_name, joint_idx in self.joint_mapping.items():
                degrees_offset = frame_data_1[joint_name]
                radians_offset = np.deg2rad(degrees_offset)
                self.play_next_pos[joint_idx] = self.anim_base_pos[joint_idx] + radians_offset
        else:
            # 如果只有一帧数据，则下一帧与当前帧相同
            self.play_next_pos = self.play_current_pos.copy()
            self.get_logger().warning("CSV只有一帧数据，将重复播放该帧")
        
        self.get_logger().info(f"播放阶段初始化完成，从第0帧开始，共{len(self.df)}帧")
    def _shutdown(self):
        # 给一点时间让最后的日志消息发送出去
        time.sleep(1.0)
        # 关闭ROS
        rclpy.shutdown()
def main(args=None):
    print("按回车键开始线性插值控制...")
    input()

    rclpy.init(args=args)
    csv_file_path = "/home/<USER>/Documents/test_output/萌萌_休闲站立.csv"
    node = CSVPlaybackControl(csv_file_path)
    node.plot_joint_angles()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # 确保节点被正确销毁
        node.destroy_node()
        # 如果程序没有通过自动关闭而是被中断，确保 rclpy 被关闭
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == "__main__":
    main()



