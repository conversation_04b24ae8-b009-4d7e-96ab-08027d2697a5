import threading
import numpy as np
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd
import time
import csv

# 控制参数
dt = 0.002  # 2ms发送一次
default_steps = 1000  # 默认插值步数


class LinearInterpolationCSV(Node):
    def __init__(self):
        super().__init__("linear_interpolation_csv")

        # 创建发布者
        self.pub = self.create_publisher(LowCmd, "/sim/lowcmd", 1)
        self.timer = self.create_timer(dt, self.timer_callback)

        # 初始化命令消息
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF

        # 初始化电机命令
        for i in range(14):
            motor_cmd = MotorCmd()
            motor_cmd.mode = 0x01
            motor_cmd.q = 0.0
            motor_cmd.kp = 0.0
            motor_cmd.dq = 0.0
            motor_cmd.kd = 0.0
            motor_cmd.tau = 0.0
            self.cmd.motor_cmd.append(motor_cmd)

        # 插值状态变量
        self.current_joint_pos = None
        self.target_joint_pos = None
        self.step_count = 0
        self.total_steps = 0
        self.is_interpolating = False
        self.finished = False
        self.auto_shutdown = True  # 控制是否自动关闭
        self.get_logger().info("线性插值CSV控制节点已初始化")

    def interpolate_to_position(self, current_pos, target_pos, steps=default_steps):
        """开始插值到目标位置"""
        self.current_joint_pos = np.array(current_pos, dtype=float)
        self.target_joint_pos = np.array(target_pos, dtype=float)
        self.step_count = 0
        self.total_steps = steps
        self.is_interpolating = True

        # 设置初始位置
        for i in range(14):
            self.cmd.motor_cmd[i].q = self.current_joint_pos[i]

        self.get_logger().info(f"开始插值，总步数: {self.total_steps}")

    def timer_callback(self):
        if self.finished or not self.is_interpolating:
            return

        if self.step_count < self.total_steps:
            # 计算当前步骤的插值比例
            ratio = self.step_count / self.total_steps

            # 线性插值计算当前关节位置
            interpolated_pos = (
                1 - ratio
            ) * self.current_joint_pos + ratio * self.target_joint_pos

            # 更新电机命令
            for i in range(14):
                self.cmd.motor_cmd[i].q = interpolated_pos[i]
                self.cmd.motor_cmd[i].kp = 30.0  # 位置增益
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 1.0  # 速度增益
                self.cmd.motor_cmd[i].tau = 0.0

            # 发布命令
            self.pub.publish(self.cmd)

            # 更新步骤计数
            self.step_count += 1

            if self.step_count % 100 == 0:
                self.get_logger().info(f"进度: {self.step_count}/{self.total_steps}")

        elif self.step_count == self.total_steps:
            self.get_logger().info("插值完成")
            self.is_interpolating = False
            self.step_count += 1
            # 等待最后一个命令发送完成
            time.sleep(0.5)
            # 标记为已完成
            self.finished = True
            # 只有在需要自动关闭时才关闭ROS
            if self.auto_shutdown:
                self.get_logger().info("程序即将退出")
                threading.Thread(target=self._shutdown).start()

    def _shutdown(self):
        time.sleep(1.0)
        rclpy.shutdown()


def load_csv_diff_values(csv_file_path):
    """从CSV文件加载diff_values"""
    diff_values_list = []
    try:
        with open(csv_file_path, "r") as file:
            reader = csv.reader(file)
            header = next(reader)  # 跳过标题行
            for row in reader:
                # 跳过frame列，只取14个关节角度值
                diff_values = [float(val) for val in row[1:15]]
                diff_values_list.append(diff_values)
        print(f"成功加载 {len(diff_values_list)} 组diff_values")
        return diff_values_list
    except FileNotFoundError:
        print(f"CSV文件未找到: {csv_file_path}")
        return []
    except Exception as e:
        print(f"读取CSV文件出错: {e}")
        return []


def main(args=None):
    print("按回车键开始线性插值控制...")
    input()

    rclpy.init(args=args)
    node = LinearInterpolationCSV()

    # 趴姿
    initial_pos = [
        -0.161,
        1.19,
        -2.76,
        0.161,
        1.19,
        -2.76,
        -0.161,
        1.19,
        -2.76,
        0.161,
        1.19,
        -2.76,
        0,
        0,
    ]
    # 站立
    target_pos = [
        -0.04,
        0.8,
        -1.5,
        0.04,
        0.8,
        -1.5,
        -0.04,
        0.8,
        -1.5,
        0.04,
        0.8,
        -1.5,
        0,
        0,
    ]

    node.auto_shutdown = False

    # 第一次插值：从初始位置到站立位置
    node.interpolate_to_position(initial_pos, target_pos)

    # 等待第一次插值完成
    while not node.finished:
        rclpy.spin_once(node, timeout_sec=0.1)

    # 加载CSV文件中的diff_values
    csv_file_path = "/tmp/joint_angles.csv"
    diff_values_list = load_csv_diff_values(csv_file_path)

    if not diff_values_list:
        print("没有找到有效的diff_values，程序退出")
        node.destroy_node()
        rclpy.shutdown()
        return

    # 当前位置从站立位置开始
    current_pos = target_pos.copy()

    # 逐个应用每组diff_values
    for i, diff_values in enumerate(diff_values_list):
        print(f"按回车键应用第 {i+1}/{len(diff_values_list)} 组变化值...")
        input()

        # 计算新的目标位置：当前位置 + 变化值
        new_target_pos = [current_pos[j] + diff_values[j] for j in range(14)]

        # 重置完成状态
        node.finished = False
        # 只有最后一组才自动关闭
        node.auto_shutdown = i == len(diff_values_list) - 1

        # 插值到新位置
        node.interpolate_to_position(current_pos, new_target_pos)

        # 等待插值完成
        while not node.finished:
            rclpy.spin_once(node, timeout_sec=0.1)

        # 更新当前位置
        current_pos = new_target_pos.copy()
        print(f"第 {i+1} 组变化值应用完成")

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == "__main__":
    main()
