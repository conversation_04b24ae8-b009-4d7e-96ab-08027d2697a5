import numpy as np
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd
import time
import threading

# 初始状态的关节位置
initial_joint_pos = np.array(
    [-0.161, 1.19, -2.76, 0.161, 1.19, -2.76, -0.161, 1.19, -2.76, 0.161, 1.19, -2.76],
    dtype=float,
)

# 目标状态的关节位置
target_joint_pos = np.array(
    [-0.04, 0.8, -1.5, 0.04, 0.8, -1.5, -0.04, 0.8, -1.5, 0.04, 0.8, -1.5], dtype=float
)

# 控制参数
dt = 0.002  # 2ms发送一次
total_steps = 1000  # 总共发送1000次


class LinearInterpolationControl(Node):
    def __init__(self):
        super().__init__("linear_interpolation_control")

        # 创建发布者
        self.pub = self.create_publisher(LowCmd, "/sim/lowcmd", 1)
        self.timer = self.create_timer(dt, self.timer_callback)

        # 初始化命令消息
        self.cmd = LowCmd()
        self.cmd.head = [0xFE, 0xEF]
        self.cmd.level_flag = 0xFF

        # 初始化电机命令
        for i in range(12):
            motor_cmd = MotorCmd()
            motor_cmd.mode = 0x01
            motor_cmd.q = initial_joint_pos[i]
            motor_cmd.kp = 0.0
            motor_cmd.dq = 0.0
            motor_cmd.kd = 0.0
            motor_cmd.tau = 0.0
            self.cmd.motor_cmd.append(motor_cmd)

        # 步骤计数器
        self.step_count = 0
        # 添加一个标志来控制程序退出
        self.finished = False
        self.get_logger().info("线性插值控制节点已初始化")

    def timer_callback(self):
        if self.finished:
            return

        if self.step_count < total_steps:
            # 计算当前步骤的插值比例
            ratio = self.step_count / total_steps

            # 线性插值计算当前关节位置
            current_joint_pos = (
                1 - ratio
            ) * initial_joint_pos + ratio * target_joint_pos

            # 更新电机命令
            for i in range(12):
                self.cmd.motor_cmd[i].q = current_joint_pos[i]
                self.cmd.motor_cmd[i].kp = 30.0  # 位置增益
                self.cmd.motor_cmd[i].dq = 0.0
                self.cmd.motor_cmd[i].kd = 1.0  # 速度增益
                self.cmd.motor_cmd[i].tau = 0.0

            # 发布命令
            self.pub.publish(self.cmd)

            # 更新步骤计数
            self.step_count += 1

            if self.step_count % 100 == 0:
                self.get_logger().info(f"进度: {self.step_count}/{total_steps}")

        elif self.step_count == total_steps:
            self.get_logger().info("线性插值完成")
            self.step_count += 1
            # 等待最后一个命令发送完成
            time.sleep(0.5)
            # 标记为已完成
            self.finished = True
            # 创建一个单独的线程来关闭ROS
            threading.Thread(target=self._shutdown).start()

    def _shutdown(self):
        # 给一点时间让最后的日志消息发送出去
        time.sleep(1.0)
        # 关闭ROS
        rclpy.shutdown()


def main(args=None):
    print("按回车键开始线性插值控制...")
    input()

    rclpy.init(args=args)
    node = LinearInterpolationControl()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # 确保节点被正确销毁
        node.destroy_node()
        # 如果程序没有通过自动关闭而是被中断，确保 rclpy 被关闭
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == "__main__":
    main()
