import time
import sys
import numpy as np
import pickle
import rclpy
from rclpy.node import Node
from lowlevel_msg.msg import LowCmd, MotorCmd


class CalibVita(Node):
    def __init__(self):
        super().__init__("calib_vita")

        # 创建一个发布者用于发送LowCmd消息
        self.action_publisher = self.create_publisher(LowCmd, "/sim/lowcmd", 1)

        # 初始化参数
        self.default_dof_pos = np.array(
            [0.1, 0.8, -1.5, -0.1, 0.8, -1.5, 0.1, 1.0, -1.5, -0.1, 1.0, -1.5]
        )
        self.joint_idxs = np.array([3, 4, 5, 0, 1, 2, 9, 10, 11, 6, 7, 8])
        self.clip_actions = 10.0
        self.cmd_id = 0

        # 加载校准动作数据
        self.load_calibration_actions()

    def load_calibration_actions(self):
        try:
            with open("cal_action.pkl", "rb") as f:
                self.all_cal_actions = pickle.load(f)
            self.get_logger().info(
                f"成功加载校准动作，共{len(self.all_cal_actions)}个动作"
            )
        except Exception as e:
            self.get_logger().error(f"加载校准动作失败: {e}")
            self.all_cal_actions = []

    def run_calibration(self):
        if not self.all_cal_actions:
            self.get_logger().error("没有校准动作可执行")
            return

        for n, cal_actions in enumerate(self.all_cal_actions):
            actions = np.clip(cal_actions[0, :], -self.clip_actions, self.clip_actions)

            # 创建LowCmd消息
            msg = LowCmd()
            msg.head = [0xFE, 0xEF]
            msg.level_flag = 0xFF
            msg.gpio = 0

            # 初始化电机命令
            msg.motor_cmd = [MotorCmd() for _ in range(20)]

            # 计算关节位置目标
            joint_pos_target = (actions * 0.25).flatten()
            joint_pos_target[[0, 3, 6, 9]] *= 0.5
            joint_pos_target += self.default_dof_pos
            joint_pos_target = joint_pos_target[self.joint_idxs]

            # 设置电机命令
            for i in range(12):
                msg.motor_cmd[i].q = float(joint_pos_target[i])
                msg.motor_cmd[i].dq = 0.0
                msg.motor_cmd[i].kp = float(30)
                msg.motor_cmd[i].kd = float(1)
                msg.motor_cmd[i].tau = 0.0

            # 发布消息
            self.action_publisher.publish(msg)
            self.get_logger().info(f"发布校准动作 {n}")
            time.sleep(0.02)

        self.get_logger().info("校准动作序列执行完毕")


def main(args=None):
    print("按回车键开始校准...")
    input()

    rclpy.init(args=args)
    node = CalibVita()

    try:
        node.run_calibration()
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
