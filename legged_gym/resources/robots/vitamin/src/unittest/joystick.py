import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
import pygame
import traceback


class VirtualJoy(Node):
    def __init__(self):
        super().__init__("virtual_joy")
        self.publisher_ = self.create_publisher(Joy, "/joy", 10)

        # 定时器 - 50Hz发送频率
        self.timer = self.create_timer(0.02, self.send_joy_msg)  # 1/50 = 0.02秒

        # 默认摇杆和按钮状态
        self.default_axes = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        self.default_buttons = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

        # 当前状态（每次发送时重置为默认值，然后根据按键修改）
        self.current_axes = self.default_axes.copy()
        self.current_buttons = self.default_buttons.copy()

        # 初始化 PyGame
        pygame.init()
        self.screen = pygame.display.set_mode((400, 400))
        pygame.display.set_caption("ROS2 Virtual JoyStick Simulator")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.SysFont("Arial", 16)

        # 按键映射 - 基本模式
        self.key_mapping = {
            # 左摇杆
            pygame.K_a: (0, 1.0),  # 左摇杆左
            pygame.K_d: (0, -1.0),  # 左摇杆右
            pygame.K_w: (1, 1.0),  # 左摇杆上
            pygame.K_s: (1, -1.0),  # 左摇杆下
            # 右摇杆
            pygame.K_LEFT: (3, 1.0),  # 右摇杆左
            pygame.K_RIGHT: (3, -1.0),  # 右摇杆右
            pygame.K_UP: (4, 1.0),  # 右摇杆上
            pygame.K_DOWN: (4, -1.0),  # 右摇杆下
        }

        # 扳机键映射
        self.trigger_mapping = {
            pygame.K_1: 2,  # LT
            pygame.K_EQUALS: 5,  # RT
        }

        # 按钮映射 - 基本模式
        self.button_mapping = {
            pygame.K_SPACE: 0,  # A (空格键)
            pygame.K_b: 1,  # B
            pygame.K_x: 2,  # X
            pygame.K_y: 3,  # Y
            pygame.K_q: 4,  # LB
            pygame.K_LEFTBRACKET: 5,  # RB
        }

        # 按钮名称 - 基本模式
        self.button_names = {
            0: "A",
            1: "B",
            2: "X",
            3: "Y",
            4: "LB",
            5: "RB",
        }

        # 摇杆名称 - 基本模式
        self.axis_names = {
            0: "Left Stick X",
            1: "Left Stick Y",
            2: "LT",
            3: "Right Stick X",
            4: "Right Stick Y",
            5: "RT",
        }

        # 按键说明 - 基本模式
        self.key_instructions = {
            0: "Space",  # 只有A键显示按键说明
            4: "q key",
            5: "[ key",
            "left_stick": "WASD",
            "right_stick": "Arrow",
            "lt": "1 key",
            "rt": "= key",
        }

        self.get_logger().info("Virtual JoyStick Simulator started - 50Hz publishing")
        self.get_logger().info("Key Mapping:")
        self.get_logger().info("WASD: Left Stick, Arrow Keys: Right Stick")
        self.get_logger().info("1: LT, =: RT, Q: LB, [: RB")
        self.get_logger().info("Space: A, B: B, X: X, Y: Y")
        self.get_logger().info("Press ESC to exit")

    def send_joy_msg(self):
        """定时器回调函数 - 50Hz发送Joy消息"""
        try:
            # 重置为默认值
            self.current_axes = self.default_axes.copy()
            self.current_buttons = self.default_buttons.copy()

            # 获取当前按键状态
            keys = pygame.key.get_pressed()

            # 更新轴状态
            for key, (axis, value) in self.key_mapping.items():
                if keys[key] and 0 <= axis < len(self.current_axes):
                    self.current_axes[axis] = value

            # 更新扳机键状态
            for key, axis in self.trigger_mapping.items():
                if 0 <= axis < len(self.current_axes):
                    self.current_axes[axis] = -1.0 if keys[key] else 0.0

            # 更新按钮状态
            for key, button in self.button_mapping.items():
                if keys[key] and 0 <= button < len(self.current_buttons):
                    self.current_buttons[button] = 1

            # 创建并发送Joy消息
            joy_msg = Joy()
            joy_msg.header.stamp = self.get_clock().now().to_msg()
            joy_msg.axes = self.current_axes
            joy_msg.buttons = self.current_buttons

            self.publisher_.publish(joy_msg)

        except Exception as e:
            self.get_logger().error(f"Error in send_joy_msg: {e}")

    def draw_joystick(self, x, y, name, axis_x, axis_y, instructions):
        """绘制摇杆"""
        try:
            # 绘制摇杆背景
            pygame.draw.circle(self.screen, (100, 100, 100), (x, y), 50)

            # 绘制摇杆位置
            stick_x = x - axis_x * 40
            stick_y = y - axis_y * 40
            pygame.draw.circle(self.screen, (200, 200, 200), (stick_x, stick_y), 20)

            # 绘制摇杆名称和说明
            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y + 70))
            self.screen.blit(text, text_rect)

            inst_text = self.font.render(instructions, True, (200, 200, 200))
            inst_rect = inst_text.get_rect(center=(x, y + 90))
            self.screen.blit(inst_text, inst_rect)

        except Exception as e:
            self.get_logger().error(f"Error in draw_joystick: {e}")

    def draw_button(self, x, y, name, state, instructions=None):
        """绘制按钮"""
        try:
            color = (200, 50, 50) if state else (100, 100, 100)
            pygame.draw.circle(self.screen, color, (x, y), 20)

            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y))
            self.screen.blit(text, text_rect)

            # 只有当instructions不为None时才显示按键说明
            if instructions is not None:
                inst_text = self.font.render(instructions, True, (200, 200, 200))
                inst_rect = inst_text.get_rect(center=(x, y + 25))
                self.screen.blit(inst_text, inst_rect)
        except Exception as e:
            self.get_logger().error(f"Error in draw_button: {e}")

    def draw_trigger(self, x, y, name, value, instructions):
        """绘制扳机键"""
        try:
            color = (200, 50, 50) if value < 0 else (100, 100, 100)
            pygame.draw.circle(self.screen, color, (x, y), 20)

            text = self.font.render(name, True, (255, 255, 255))
            text_rect = text.get_rect(center=(x, y))
            self.screen.blit(text, text_rect)

            inst_text = self.font.render(instructions, True, (200, 200, 200))
            inst_rect = inst_text.get_rect(center=(x, y + 25))
            self.screen.blit(inst_text, inst_rect)

        except Exception as e:
            self.get_logger().error(f"Error in draw_trigger: {e}")

    def draw_interface(self):
        """绘制完整界面"""
        try:
            # 清空屏幕
            self.screen.fill((30, 30, 50))

            # 绘制标题
            title_font = pygame.font.SysFont("Arial", 20)
            title = title_font.render(
                "ROS2 Virtual JoyStick Simulator", True, (255, 255, 255)
            )
            title_rect = title.get_rect(center=(200, 25))
            self.screen.blit(title, title_rect)

            # 绘制控制元素
            self.draw_controls()

            # 绘制当前值
            self.draw_joy_values()

            # 绘制帮助信息
            help_text = self.font.render("Press ESC to exit", True, (200, 200, 200))
            help_rect = help_text.get_rect(center=(200, 380))
            self.screen.blit(help_text, help_rect)

        except Exception as e:
            self.get_logger().error(f"Error in draw_interface: {e}")

    def draw_controls(self):
        """绘制控制元素"""
        try:
            # 布局参数
            left_stick_x, right_stick_x = 120, 280
            stick_y = 100  # 摇杆位置
            button_y = 200  # 按钮位置

            # 绘制摇杆
            self.draw_joystick(
                left_stick_x,
                stick_y,
                "Left Stick",
                self.current_axes[0],
                self.current_axes[1],
                self.key_instructions["left_stick"],
            )

            self.draw_joystick(
                right_stick_x,
                stick_y,
                "Right Stick",
                self.current_axes[3],
                self.current_axes[4],
                self.key_instructions["right_stick"],
            )

            # 左侧扳机和肩键 - 在Left Stick左边
            # LT在上，LB在下
            self.draw_trigger(
                40,  # Left Stick左边
                stick_y - 30,  # 上方
                "LT",
                self.current_axes[2],
                self.key_instructions["lt"],
            )
            self.draw_button(
                40,  # Left Stick左边
                stick_y + 30,  # 下方
                "LB",
                self.current_buttons[4],
                self.key_instructions[4],
            )

            # 右侧扳机和肩键 - 在Right Stick右边
            # RT在上，RB在下
            self.draw_trigger(
                360,  # Right Stick右边
                stick_y - 30,  # 上方
                "RT",
                self.current_axes[5],
                self.key_instructions["rt"],
            )
            self.draw_button(
                360,  # Right Stick右边
                stick_y + 30,  # 下方
                "RB",
                self.current_buttons[5],
                self.key_instructions[5],
            )

            # 绘制面按钮 - JoyCon风格菱形布局
            center_x = 200
            center_y = button_y
            offset = 30  # 按钮间距

            buttons = [
                (center_x, center_y + offset, "B", 1),  # 下方
                (center_x + offset, center_y, "A", 0),  # 右方
                (center_x - offset, center_y, "Y", 3),  # 左方
                (center_x, center_y - offset, "X", 2),  # 上方
            ]
            for x, y, name, idx in buttons:
                # 只有A键显示按键说明(Space)，其他按钮不显示
                instructions = self.key_instructions.get(idx) if idx == 0 else None
                self.draw_button(
                    x,
                    y,
                    name,
                    self.current_buttons[idx],
                    instructions,
                )

        except Exception as e:
            self.get_logger().error(f"Error in draw_controls: {e}")

    def draw_joy_values(self):
        """绘制当前Joy消息值 - 分两列显示"""
        try:
            # 起始位置
            start_y = 230

            # 左列：axes值
            left_x = 20
            axes_start_y = start_y
            axes_title = self.font.render("Axes:", True, (255, 255, 100))
            self.screen.blit(axes_title, (left_x, axes_start_y))

            y_pos = axes_start_y + 20
            for i in range(6):  # 只显示基本模式的6个轴
                if i in self.axis_names:
                    text = self.font.render(
                        f"axes[{i}]: {self.current_axes[i]:.2f} - {self.axis_names[i]}",
                        True,
                        (200, 200, 200),
                    )
                    self.screen.blit(text, (left_x, y_pos))
                    y_pos += 18

            # 右列：buttons值 - 与左列对齐
            right_x = 270
            buttons_start_y = axes_start_y  # 与axes标题同一高度
            buttons_title = self.font.render("Buttons:", True, (255, 255, 100))
            self.screen.blit(buttons_title, (right_x, buttons_start_y))

            y_pos = buttons_start_y + 20
            for i in range(6):  # 只显示基本模式的6个按钮
                if i in self.button_names:
                    text = self.font.render(
                        f"buttons[{i}]: {self.current_buttons[i]} - {self.button_names[i]}",
                        True,
                        (200, 200, 200),
                    )
                    self.screen.blit(text, (right_x, y_pos))
                    y_pos += 18

        except Exception as e:
            self.get_logger().error(f"Error in draw_joy_values: {e}")

    def run(self):
        """主运行循环"""
        running = True
        while running and rclpy.ok():
            try:
                # 处理事件
                for event in pygame.event.get():
                    if event.type == pygame.QUIT:
                        running = False
                    elif event.type == pygame.KEYDOWN:
                        if event.key == pygame.K_ESCAPE:
                            running = False

                # 绘制界面
                self.draw_interface()
                pygame.display.flip()

                # 处理ROS消息
                rclpy.spin_once(self, timeout_sec=0)
                self.clock.tick(30)  # 30fps显示刷新率

            except Exception as e:
                self.get_logger().error(f"Error in run loop: {e}")
                pygame.time.delay(100)

        pygame.quit()
        self.get_logger().info("Virtual JoyStick Simulator closed")


def main(args=None):
    rclpy.init(args=args)
    try:
        node = VirtualJoy()
        node.run()
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()
    finally:
        rclpy.shutdown()


if __name__ == "__main__":
    main()
