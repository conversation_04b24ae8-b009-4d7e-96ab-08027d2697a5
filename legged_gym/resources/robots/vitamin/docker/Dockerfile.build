FROM ros:humble

ARG DEBIAN_FRONTEND=noninteractive
ARG HTTP_PROXY

# 将源文件放入临时位置
COPY sources.list sources_arm64.list /

# 根据架构自动选择合适的源文件
RUN echo "Architecture detection: $(uname -m)" && \
    if [ "$(uname -m)" = "aarch64" ] || [ "$(uname -m)" = "arm64" ]; then \
      echo "===== CONDITION MET: Running ARM branch =====" && \
      echo "Detected ARM architecture ($(uname -m)), using ARM sources" && \
      cp /sources_arm64.list /etc/apt/sources.list && \
      echo "Sources copied successfully"; \
    else \
      echo "===== CONDITION NOT MET: Running x86 branch =====" && \
      echo "Detected x86 architecture ($(uname -m)), using x86 sources" && \
      cp /sources.list /etc/apt/sources.list && \
      echo "Sources copied successfully"; \
    fi

RUN apt-get update && apt-get install -y --no-install-recommends \
    xorg-dev \
    libglu1-mesa-dev \
    libgl1-mesa-dev \
    mesa-common-dev \
    binutils-dev libiberty-dev \
 && rm -rf /var/lib/apt/lists/*

# 设置 GitHub 和 GitLab 的代理
RUN if [ -n "$HTTP_PROXY" ]; then \
      git config --global http.https://github.com.proxy $HTTP_PROXY; \
    fi && \
    if [ -n "$HTTP_PROXY" ]; then \
      git config --global http.https://gitlab.com.proxy $HTTP_PROXY; \
    fi
