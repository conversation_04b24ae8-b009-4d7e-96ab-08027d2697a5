# 定义可复用的配置模板
x-common-build: &common-build
  build:
    context: .
    dockerfile: Dockerfile.build
    args:
      - HTTP_PROXY=${HTTP_PROXY:-}
  volumes:
    - ..:/vitamin
  working_dir: /vitamin
  environment:
    - ROS_DISTRO=humble
    - DEBIAN_FRONTEND=noninteractive
    - HTTP_PROXY=${HTTP_PROXY:-}

x-common-arm64: &common-arm64
  image: ***********:8000/algo/docker-run_arm64:latest
  network_mode: host
  volumes:
    - ..:/vitamin
    - /app/fastrtps_profile.xml:/app/fastrtps_profile.xml
  environment:
    - ROS_DISTRO=humble
    - DEBIAN_FRONTEND=noninteractive
    - HOST_IP=${HOST_IP:-HOST_IP}
    - PYTHONPATH=/opt/miniconda3/envs/himloco/lib/python3.10/site-packages:${PYTHONPATH:-}
    - PULSE_SERVER=unix:/tmp/pulse-socket
    - FASTRTPS_DEFAULT_PROFILES_FILE=${FASTRTPS_DEFAULT_PROFILES_FILE}
    - ROS_DOMAIN_ID=${ROS_DOMAIN_ID}
    - LD_PRELOAD=/usr/lib/aarch64-linux-gnu/libstdc++.so.6
  working_dir: /vitamin
  shm_size: 2gb
  tty: true
  stdin_open: true

x-gpu-config: &gpu-config
  deploy:
    resources:
      reservations:
        devices:
          - driver: nvidia
            count: 1
            capabilities: [gpu, utility, compute, graphics, display]
  runtime: nvidia
  environment:
    - NVIDIA_VISIBLE_DEVICES=all
    - NVIDIA_DRIVER_CAPABILITIES=all

services:
  # 交互式开发环境（保留用于调试）
  build_debug:
    <<: *common-build
    container_name: vitamin_container
    command: /bin/bash
    tty: true
    stdin_open: true

  # 非交互式构建任务
  build:
    <<: *common-build
    command: bash -c "make build"

  # 清理 sim 构建任务
  clean:
    <<: *common-build
    command: bash -c "make clean_sim"

  # 运行编译产物的容器（使用TurboVNC和VirtualGL进行高效渲染）
  run:
    <<: *gpu-config
    build:
      context: .
      dockerfile: Dockerfile.run
    container_name: vitamin_run_container
    volumes:
      - ..:/vitamin
      - ~/minio-mount/vita-data:/vita-data
      - ~/minio-mount/vita-algo-data:/vita-algo-data
      - ~/minio-mount/vita-probe:/vita-probe
    environment:
      - ROS_DISTRO=humble
      - DEBIAN_FRONTEND=noninteractive
      - USE_VIRTUALGL=true
      # 设置 VNC 连接时的密码
      - VNC_PASSWORD=${VNC_PASSWORD:-Vita@123}
      # 设置 VNC 连接时的分辨率
      - VNC_GEOMETRY=${VNC_RESOLUTION:-1280x800}
      # 设置显示号 (从 1 开始)
      - VNC_DISPLAY=${VNC_DISPLAY:-1}
      - HOST_IP=${HOST_IP:-HOST_IP}
      # 设置Python路径
      - PYTHONPATH=/opt/miniconda3/envs/himloco/lib/python3.10/site-packages:${PYTHONPATH:-}
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=all
      - PULSE_SERVER=unix:/tmp/pulse-socket
    working_dir: /vitamin
    ports:
      - "590${VNC_DISPLAY:-1}:590${VNC_DISPLAY:-1}"
      - 8765:8765
    shm_size: 2gb
    tty: true
    stdin_open: true

  # 运行Arm64容器
  run_arm64:
    <<: *common-arm64
    container_name: vitamin_run_arm64_container

  # 运行Arm64容器
  himloco:
    <<: *common-arm64
    container_name: himloco_container
    command: bash -c "make himloco"
    restart: unless-stopped

  # 新增训练服务（使用 Dockerfile.train）
  train:
    <<: *gpu-config
    build:
      context: .
      dockerfile: Dockerfile.train
    container_name: vitamin_train_container
    environment:
      - DEBIAN_FRONTEND=noninteractive
      - HTTP_PROXY=${HTTP_PROXY:-}
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=all
    # 不挂载外部文件（仅保留容器内工作目录）
    volumes: []
    shm_size: 2gb
    tty: true
    stdin_open: true
    command: /bin/bash
