FROM 10.100.10.2:8000/library/ros:humble_arm64

# 设置非交互、时区和语言
ENV DEBIAN_FRONTEND=noninteractive \
    LANG=C.UTF-8 \
    TZ=Asia/Shanghai

# 复制清华源配置文件
COPY sources_arm64.list /etc/apt/sources.list

# 安装curl和gnupg2，并下载ROS GPG密钥
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl gnupg2 && \
    curl -sSL http://10.100.10.2:8082/deps/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg && \
    rm -rf /var/lib/apt/lists/*

# 添加ROS2清华源
RUN echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] https://mirrors.tuna.tsinghua.edu.cn/ros2/ubuntu jammy main" | tee /etc/apt/sources.list.d/ros2.list > /dev/null

# 安装桌面环境和基础依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    wget \
    ros-humble-foxglove-bridge && \
    rm -rf /var/lib/apt/lists/*

# 设置miniconda路径
ENV PATH="/opt/miniconda3/bin:${PATH}"
ENV CONDA_AUTO_UPDATE_CONDA=false

# 下载miniconda并安装
RUN cd /tmp && \
    wget -q "http://10.100.10.2:8082/deps/Miniconda3-py310_24.11.1-0-Linux-aarch64.sh" && \
    chmod +x Miniconda3-py310_24.11.1-0-Linux-aarch64.sh && \
    bash Miniconda3-py310_24.11.1-0-Linux-aarch64.sh -b -p /opt/miniconda3 && \
    rm -f Miniconda3-py310_24.11.1-0-Linux-aarch64.sh && \
    ln -s /opt/miniconda3/etc/profile.d/conda.sh /etc/profile.d/conda.sh && \
    echo ". /opt/miniconda3/etc/profile.d/conda.sh" >> ~/.bashrc

# 在conda中安装python3.10和torch1.13.0
RUN . /opt/miniconda3/etc/profile.d/conda.sh && \
    conda create -n himloco python=3.10 -y && \
    conda activate himloco && \
    conda install -c conda-forge libstdcxx-ng -y && \
    pip3 install -i https://pypi.tuna.tsinghua.edu.cn/simple \
        torch==1.13.0 \
        torchvision==0.14.0 \
        torchaudio==0.13.0 \
        setuptools==65.5.0 \
        lark \
        scipy \
        empy==3.3.4 \
        catkin_pkg \
        onnxruntime \
        "numpy<2.0.0" \
        pybind11 \
        colcon-common-extensions

ENV PYTHONPATH="/opt/miniconda3/envs/himloco/lib/python3.10/site-packages/:${PYTHONPATH}"

# 设置启动命令
CMD ["/bin/bash", "-c", "cd /vitamin && exec /bin/bash"]
