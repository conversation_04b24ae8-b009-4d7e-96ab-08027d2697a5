#!/bin/bash

# 获取主机 IP 地址
HOST_IP=$(ip -4 addr show scope global | grep inet | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | head -n 1)

# 如果没有获取到 IP，使用 localhost 作为默认值
if [ -z "$HOST_IP" ]; then
  HOST_IP="localhost"
  echo "警告: 未能获取主机 IP 地址，使用 localhost 作为默认值"
fi

# 检查 .env 文件是否存在
ENV_FILE="$(dirname "$0")/.env"

# 如果 .env 文件存在，检查是否已经包含 HOST_IP
if [ -f "$ENV_FILE" ]; then
  if grep -q "^HOST_IP=" "$ENV_FILE"; then
    # 更新现有的 HOST_IP 行
    sed -i "s/^HOST_IP=.*/HOST_IP=$HOST_IP/" "$ENV_FILE"
    echo "已更新 .env 文件中的 HOST_IP 为: $HOST_IP"
  else
    # 添加 HOST_IP 行
    echo "HOST_IP=$HOST_IP" >> "$ENV_FILE"
    echo "已添加 HOST_IP=$HOST_IP 到 .env 文件"
  fi
else
  # 创建新的 .env 文件
  echo "HOST_IP=$HOST_IP" > "$ENV_FILE"
  echo "已创建 .env 文件并设置 HOST_IP=$HOST_IP"
fi

# 显示设置的环境变量
echo "环境变量已设置完成:"
echo "HOST_IP=$HOST_IP"
echo ""