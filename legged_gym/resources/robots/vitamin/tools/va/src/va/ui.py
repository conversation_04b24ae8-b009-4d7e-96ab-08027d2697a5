"""Web UI界面模块"""

import json
import sys
from nicegui import ui
from mcap.reader import make_reader

from rclpy.serialization import deserialize_message
from rosidl_runtime_py.utilities import get_message
from rosidl_runtime_py import message_to_ordereddict

from .mcap_utils import McapInfo, MessageFinder
from .field_path import FieldSetter


class WebUI:
    """MCAP文件Web UI界面"""

    def __init__(self, file_path):
        """初始化Web UI"""
        self.file_path = file_path
        self.mcap_info = McapInfo(file_path)
        self.mcap_info.load()
        self.topics = self._get_topics()
        self.selected_topic = None
        self.message_index = 0
        self.finder = MessageFinder(file_path)
        self.message_timestamps = []
        self.message_display = None
        self.edit_mode = False
        self.current_msg_dict = None
        self.edit_form = None
        self.modified_fields = {}

        self.slider = None
        self.slider_label = None

        self.ts_input = None

        self.topic_message_counts = {}
        self._init_topic_counts()

    def _init_topic_counts(self):
        """初始化主题消息计数"""
        for channel in self.mcap_info.summary.channels.values():
            topic = channel.topic
            count = self.mcap_info.summary.statistics.channel_message_counts[channel.id]
            self.topic_message_counts[topic] = count

    def _get_topics(self):
        """获取MCAP文件中的所有主题"""
        topics = []
        for channel in self.mcap_info.summary.channels.values():
            topics.append(channel.topic)
        return sorted(topics)

    def _update_slider_label(self):
        """更新滑块标签显示"""
        if not hasattr(self, "slider_label") or not self.slider_label:
            return

        label_text = "消息: 0/0"
        if self.selected_topic:
            total_count = self.topic_message_counts.get(self.selected_topic, 0)
            current_pos = self.message_index + 1
            label_text = f"消息: {current_pos}/{total_count}"

            # 直接从当前消息获取时间戳
            if hasattr(self, "current_message"):
                timestamp = self.finder.format_timestamp(
                    self.current_message["log_time"]
                )
                label_text += f" ({timestamp})"

        self.slider_label.text = label_text

    async def _get_message_by_index(self, index):
        """直接获取指定索引的消息
        使用优化后的缓存机制

        Args:
            index: 消息索引

        Returns:
            消息对象, 如果失败则返回 None
        """
        if not self.selected_topic:
            return None

        try:
            msg_data = await self._get_single_message(index)
            if msg_data:
                timestamp = self.finder.format_timestamp(msg_data["log_time"])
                msg_data["timestamp"] = timestamp
                return msg_data
            else:
                return None
        except Exception as e:
            print(f"获取消息错误: {e}")
            return None

    async def _on_slider_change(self, event):
        """滑块改变回调"""
        try:
            new_index = event.args
            if new_index != self.message_index:
                self.message_index = new_index
                ui.notify(f"正在加载消息...", type="info")
                msg_data = await self._get_message_by_index(new_index)

                if msg_data:
                    self.current_message = msg_data
                    self._update_slider_label()
                    await self._display_message_content(msg_data)
                    ui.notify(f"已加载消息", type="positive")
                else:
                    ui.notify(f"无法获取索引为 {new_index} 的消息", type="negative")
        except Exception as e:
            print(f"滑块改变错误: {e}")
            ui.notify(f"滑块操作错误: {str(e)}", type="negative")

    async def _on_topic_selected(self, event):
        """主题选择回调"""
        self.selected_topic = event.value
        ui.notify(f"已选择主题: {self.selected_topic}")

        if self.selected_topic:
            total_count = self.topic_message_counts.get(self.selected_topic, 0)
            max_value = max(0, total_count - 1)

            if self.slider:
                self.slider._props["max"] = max_value
                self.slider.value = 0
                self.slider.update()

            self.message_index = 0

            msg_data = await self._get_message_by_index(0)
            if msg_data:
                self.current_message = msg_data
                self._update_slider_label()
                await self._display_message_content(msg_data)
                ui.notify(f"已加载消息", type="positive")
            else:
                ui.notify("未找到消息", type="warning")
                if self.message_display:
                    self.message_display.content = "*该主题下没有消息*"
                    self.message_display.update()

    async def _search_timestamp(self, event=None):
        """搜索时间戳"""
        if event:
            if hasattr(event, "value"):
                timestamp = event.value.strip()
            else:
                timestamp = str(event).strip()
        elif hasattr(self, "ts_input") and self.ts_input:
            timestamp = self.ts_input.value.strip()
        else:
            return

        if not timestamp or not self.selected_topic:
            return

        try:
            logtime_ns = self.finder.parse_timestamp(timestamp)

            ui.notify("正在搜索时间戳...", type="info")

            found_index = await self._binary_search_timestamp(logtime_ns)

            if found_index >= 0:
                if self.slider:
                    self.slider.value = found_index
                    self.slider.update()

                self.message_index = found_index

                # 直接加载这条消息
                msg_data = await self._get_message_by_index(found_index)
                if msg_data:
                    self.current_message = msg_data
                    self._update_slider_label()
                    await self._display_message_content(msg_data)
                    ui.notify(
                        f"找到时间戳附近的消息 (索引: {found_index})", type="positive"
                    )
                else:
                    ui.notify("无法显示找到的消息", type="warning")
            else:
                ui.notify("未找到匹配的消息", type="warning")
        except Exception as e:
            ui.notify(f"搜索时间戳出错: {str(e)}", type="negative")
            print(f"搜索时间戳错误: {e}")

    async def _binary_search_timestamp(self, target_time):
        """使用真正的二分搜索查找时间最接近的消息索引

        Args:
            target_time: 目标时间戳（纳秒）

        Returns:
            最接近的消息索引，如果找不到返回-1
        """
        if not self.selected_topic:
            return -1

        try:
            # 获取主题消息数量
            total_count = self.topic_message_counts.get(self.selected_topic, 0)
            if total_count <= 0:
                return -1

            left, right = 0, total_count - 1
            closest_index = -1
            min_diff = float("inf")

            while left <= right:
                mid = (left + right) // 2

                mid_msg = await self._get_single_message(mid)
                if not mid_msg:
                    break

                mid_time = mid_msg["log_time"]
                time_diff = abs(mid_time - target_time)

                if time_diff < min_diff:
                    min_diff = time_diff
                    closest_index = mid

                if mid_time < target_time:
                    left = mid + 1
                elif mid_time > target_time:
                    right = mid - 1
                else:
                    closest_index = mid
                    break

            if closest_index >= 0:
                range_start = max(0, closest_index - 5)
                range_end = min(total_count - 1, closest_index + 5)

                for idx in range(range_start, range_end + 1):
                    if idx == closest_index:
                        continue

                    sample_msg = await self._get_single_message(idx)
                    if not sample_msg:
                        continue

                    time_diff = abs(sample_msg["log_time"] - target_time)
                    if time_diff < min_diff:
                        min_diff = time_diff
                        closest_index = idx

            return closest_index
        except Exception as e:
            print(f"二分搜索时间戳错误: {e}")
            return -1

    async def _get_single_message(self, index):
        """获取单个消息（优化版）

        Args:
            index: 消息索引

        Returns:
            消息对象,如果失败则返回None
        """
        if not self.selected_topic:
            return None

        try:
            total_count = self.topic_message_counts.get(self.selected_topic, 0)
            if index < 0 or index >= total_count:
                return None

            if (
                not hasattr(self, "_cached_topic")
                or self._cached_topic != self.selected_topic
            ):
                self._cached_topic = self.selected_topic
                self._cached_messages = None

            if self._cached_messages is None:
                with open(self.file_path, "rb") as f:
                    reader = make_reader(f)
                    self._cached_messages = list(
                        reader.iter_messages(topics=[self.selected_topic])
                    )

            if 0 <= index < len(self._cached_messages):
                schema, channel, message = self._cached_messages[index]
                return {
                    "schema": schema,
                    "channel": channel,
                    "message": message,
                    "log_time": message.log_time,
                }
            else:
                return None
        except Exception as e:
            print(f"获取单个消息错误: {e}")
            self._cached_messages = None
            return None

    async def _search_button_click(self):
        """搜索按钮点击回调"""
        await self._search_timestamp()

    async def _toggle_edit_mode(self):
        """切换编辑模式"""
        self.edit_mode = not self.edit_mode
        if self.edit_mode and self.current_msg_dict:
            if self.edit_form:
                self.edit_form.clear()
            else:
                self.edit_form = ui.element("div").classes("w-full")

            self.modified_fields = {}

            with self.edit_form:
                await self._create_nested_form(self.current_msg_dict)

            self.message_display.visible = False
            self.edit_form.visible = True
        else:
            if self.message_display:
                self.message_display.visible = True
            if self.edit_form:
                self.edit_form.visible = False

    async def _create_nested_form(self, data, prefix=""):
        """创建嵌套的表单字段"""
        for key, value in data.items():
            field_path = f"{prefix}.{key}" if prefix else key

            if isinstance(value, dict):
                with ui.expansion(key, value=True).classes("w-full"):
                    with ui.card().classes("ml-4"):
                        await self._create_nested_form(value, field_path)
            elif isinstance(value, (list, tuple)):
                with ui.expansion(f"{key}", value=True).classes("w-full"):
                    with ui.card().classes("ml-4"):
                        for i, item in enumerate(value):
                            array_path = f"{field_path}[{i}]"
                            if isinstance(item, dict):
                                with ui.expansion(f"[{i}]", value=True).classes(
                                    "w-full"
                                ):
                                    with ui.card().classes("ml-4"):
                                        await self._create_nested_form(item, array_path)
                            else:
                                # 根据值类型选择合适的输入控件
                                if isinstance(item, bool):
                                    ui.switch(
                                        f"[{i}]",
                                        value=item,
                                        on_change=lambda e, path=array_path: self._on_field_change(
                                            e, path
                                        ),
                                    )
                                elif isinstance(item, (int, float)):
                                    ui.number(
                                        f"[{i}]",
                                        value=item,
                                        on_change=lambda e, path=array_path: self._on_field_change(
                                            e, path
                                        ),
                                    )
                                elif isinstance(item, str):
                                    ui.input(
                                        f"[{i}]",
                                        value=item,
                                        on_change=lambda e, path=array_path: self._on_field_change(
                                            e, path
                                        ),
                                    )
                                else:
                                    ui.input(
                                        f"[{i}]",
                                        value=str(item),
                                        on_change=lambda e, path=array_path: self._on_field_change(
                                            e, path
                                        ),
                                    )
            else:
                # 根据值类型选择合适的输入控件
                if isinstance(value, bool):
                    ui.switch(
                        key,
                        value=value,
                        on_change=lambda e, path=field_path: self._on_field_change(
                            e, path
                        ),
                    )
                elif isinstance(value, (int, float)):
                    ui.number(
                        key,
                        value=value,
                        on_change=lambda e, path=field_path: self._on_field_change(
                            e, path
                        ),
                    )
                elif isinstance(value, str):
                    ui.input(
                        key,
                        value=value,
                        on_change=lambda e, path=field_path: self._on_field_change(
                            e, path
                        ),
                    )
                else:
                    ui.input(
                        key,
                        value=str(value),
                        on_change=lambda e, path=field_path: self._on_field_change(
                            e, path
                        ),
                    )

    def _on_field_change(self, e, field_path):
        """处理字段值变化，支持数组索引访问"""
        try:
            parts = []
            current_part = ""
            i = 0

            while i < len(field_path):
                if field_path[i] == "[":
                    if current_part:
                        parts.append(current_part)
                        current_part = ""
                    j = i + 1
                    while j < len(field_path) and field_path[j] != "]":
                        j += 1
                    if j < len(field_path):
                        parts.append(field_path[i + 1 : j])
                        i = j + 1
                    else:
                        raise ValueError("Invalid array index syntax")
                elif field_path[i] == ".":
                    if current_part:
                        parts.append(current_part)
                        current_part = ""
                else:
                    current_part += field_path[i]
                i += 1

            if current_part:
                parts.append(current_part)

            # 获取原始值的类型
            current = self.current_msg_dict
            for part in parts[:-1]:
                if part.isdigit():  # 是数组索引
                    current = current[int(part)]
                else:
                    current = current[part]

            last_part = parts[-1]
            target = current[int(last_part) if last_part.isdigit() else last_part]

            # 根据原始值的类型进行转换
            if isinstance(target, bool):
                value = bool(e.value)
            elif isinstance(target, int):
                value = int(float(e.value))
            elif isinstance(target, float):
                value = float(e.value)
            elif isinstance(target, str):
                value = str(e.value)
            else:
                value = e.value

            self.modified_fields[tuple(parts)] = str(value)

            if last_part.isdigit():
                current[int(last_part)] = value
            else:
                current[last_part] = value

        except Exception as e:
            ui.notify(f"字段更新失败: {str(e)}", type="negative")
            print(f"字段更新错误: {e}")

    def _collect_changes(self, field_setters):
        """收集已修改的字段

        Args:
            field_setters: List[FieldSetter] 收集的修改列表
        """
        for path_tuple, value in self.modified_fields.items():
            field_setter = FieldSetter(list(path_tuple), value)
            field_setters.append(field_setter)

    async def _save_changes(self):
        """保存消息修改"""
        if not self.current_msg_dict or not self.selected_topic:
            ui.notify("没有可保存的修改", type="warning")
            return

        try:
            field_setters = []
            self._collect_changes(field_setters)

            if hasattr(self, "current_message"):
                timestamp = self.finder.format_timestamp(
                    self.current_message["log_time"]
                )

                modified_file = self.finder.modify_message(
                    self.selected_topic, timestamp, field_setters
                )

                ui.notify("修改已保存", type="positive")

                with ui.dialog() as dialog, ui.card().classes("w-[32rem] p-4"):
                    ui.label("修改后的文件已保存至:").classes("text-lg font-bold mb-2")

                    with ui.row().classes(
                        "w-full items-center gap-2 bg-gray-100 p-3 rounded"
                    ):
                        path_text = ui.label(str(modified_file)).classes(
                            "font-mono text-sm flex-grow break-all"
                        )

                        def copy_to_clipboard():
                            """复制文件路径到剪贴板"""
                            js_code = f"""
                            try {{
                                await navigator.clipboard.writeText('{modified_file}');
                                element.run_method('_handle_copy_success');
                            }} catch (err) {{
                                element.run_method('_handle_copy_error', err.toString());
                            }}
                            """
                            ui.run_javascript(js_code)

                        ui.button(
                            on_click=copy_to_clipboard, icon="content_copy"
                        ).props("flat dense").classes("min-w-8 w-8 h-8")

                    with ui.row().classes("w-full justify-end mt-4"):
                        ui.button("确定", on_click=dialog.close).props("color=primary")

                dialog.open()
                self.edit_mode = False
                await self._toggle_edit_mode()
        except Exception as e:
            ui.notify(f"保存修改失败: {str(e)}", type="negative")

    def _handle_copy_success(self):
        """处理复制成功"""
        ui.notify("文件路径已复制到剪贴板", type="positive")

    def _handle_copy_error(self, error):
        """处理复制失败"""
        ui.notify(f"复制失败: {error}", type="negative")

    async def _display_message_content(self, msg_data):
        """显示消息内容"""
        try:
            schema = msg_data["schema"]
            message = msg_data["message"]

            msg_type = get_message(schema.name)
            msg = deserialize_message(message.data, msg_type)
            msg_dict = message_to_ordereddict(msg)

            self.current_msg_dict = msg_dict

            if self.message_display:
                pretty_json = json.dumps(msg_dict, indent=2, ensure_ascii=False)
                self.message_display.content = f"```json\n{pretty_json}\n```"
                self.message_display.update()
        except Exception as e:
            if self.message_display:
                self.message_display.content = f"显示消息失败: {str(e)}"
                self.message_display.update()
            ui.notify(f"解析消息失败: {str(e)}", type="negative")

    def _setup_ui(self):
        """设置UI界面"""
        ui.label(f"MCAP文件: {self.file_path}").classes("text-xl font-bold")

        with ui.expansion("文件信息", icon="info").classes("w-full"):
            with ui.card().classes("w-full border-none"):
                start_time, start_ts = self.mcap_info.format_timestamp(
                    self.mcap_info.summary.statistics.message_start_time
                )
                end_time, end_ts = self.mcap_info.format_timestamp(
                    self.mcap_info.summary.statistics.message_end_time
                )

                ui.label(f"开始时间: {start_time} ({start_ts})")
                ui.label(f"结束时间: {end_time} ({end_ts})")
                ui.label(f"主题数量: {len(self.topics)}")

        with ui.expansion("主题列表", icon="list").classes("w-full mt-2"):
            with ui.card().classes("w-full border-none"):
                columns = [
                    {
                        "name": "topic",
                        "label": "主题",
                        "field": "topic",
                        "sortable": True,
                    },
                    {
                        "name": "count",
                        "label": "消息数",
                        "field": "count",
                        "sortable": True,
                    },
                    {
                        "name": "schema",
                        "label": "类型",
                        "field": "schema",
                        "sortable": True,
                    },
                ]

                rows = []
                for channel in self.mcap_info.summary.channels.values():
                    rows.append(
                        {
                            "topic": channel.topic,
                            "count": self.mcap_info.summary.statistics.channel_message_counts[
                                channel.id
                            ],
                            "schema": self.mcap_info.summary.schemas[
                                channel.schema_id
                            ].name,
                        }
                    )

                ui.table(columns=columns, rows=rows, row_key="topic").classes("w-full")

        with ui.expansion("消息查看器", icon="message", value=True).classes(
            "w-full mt-2"
        ):
            with ui.card().classes("w-full"):
                layout = ui.element("div").style(
                    "display: grid; grid-template-columns: 1fr 3fr; gap: 16px; width: 100%;"
                )

                with layout:
                    with ui.card():
                        ui.label("主题选择").classes("text-lg font-bold")
                        self.topic_select = ui.select(
                            label="选择主题",
                            options=self.topics,
                            on_change=self._on_topic_selected,
                        ).classes("w-full mb-4")

                        ui.label("时间戳搜索").classes("text-lg font-bold mt-4")
                        self.ts_input = ui.input(
                            label="搜索时间戳 (秒.纳秒)",
                            placeholder="例如: 1234.567890",
                        ).classes("w-full")
                        self.ts_input.on("keydown.enter", self._search_button_click)

                        ui.button("搜索", on_click=self._search_button_click).classes(
                            "mt-2 mb-4"
                        ).props("color=primary")

                        ui.separator().classes("my-4")
                        with ui.row().classes("w-full items-center"):
                            ui.label("消息导航").classes("text-lg font-bold")
                            self.slider_label = ui.label("消息: 0/0").classes(
                                "text-sm ml-auto"
                            )

                        self.slider = ui.slider(min=0, max=0, value=0, step=1).classes(
                            "w-full mt-2"
                        )
                        self.slider.on("update:model-value", self._on_slider_change)

                    with ui.card():
                        with ui.row().classes("w-full items-center justify-between"):
                            ui.label("消息内容").classes("text-lg font-bold")
                            with ui.row().classes("gap-2"):
                                ui.button(
                                    "编辑", on_click=self._toggle_edit_mode
                                ).props("icon=edit")
                                ui.button("保存", on_click=self._save_changes).props(
                                    "icon=save color=primary"
                                )

                        self.message_display = ui.markdown(
                            "*选择主题以加载消息*"
                        ).classes(
                            "w-full max-h-[600px] overflow-auto p-4 border rounded mt-2"
                        )

                        # 创建编辑表单容器(初始隐藏)
                        self.edit_form = ui.element("div").classes("w-full mt-2")
                        self.edit_form.visible = False

    def run(self):
        """运行Web UI"""
        self._setup_ui()
        try:
            ui.run(title="MCAP文件查看器", reload=False)
        except Exception as e:
            print(f"UI启动失败: {e}")


def main():
    """当模块单独运行时的入口点"""
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        ui_instance = WebUI(file_path)
        ui_instance.run()
    else:
        print("请提供MCAP文件路径")
        sys.exit(1)


if __name__ == "__main__":
    main()
