import unittest
from va.mcap_utils.message_finder import MessageFinder


class TestMessageFinder(unittest.TestCase):
    """测试MessageFinder类"""

    def setUp(self):
        """测试前的设置"""
        self.message_finder = MessageFinder("dummy_path.mcap")

    def test_parse_timestamp(self):
        """测试时间戳解析功能"""
        # 测试用例：传入 1739279090.291503010，应该输出 1739279090291503010
        timestamp = "1739279090.291503010"
        expected_ns = 1739279090291503010

        result = self.message_finder.parse_timestamp(timestamp)
        self.assertEqual(result, expected_ns)

        # 额外测试：处理不同精度的纳秒部分
        self.assertEqual(
            self.message_finder.parse_timestamp("1739279090.2"),
            1739279090200000000
        )
        
        self.assertEqual(
            self.message_finder.parse_timestamp("1739279090.291"),
            1739279090291000000
        )


if __name__ == "__main__":
    unittest.main() 