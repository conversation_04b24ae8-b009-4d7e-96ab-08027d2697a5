"""ROS 消息处理工具"""

import re
import numpy as np
from typing import List, Any

from rosidl_runtime_py import set_message_fields


def dynamic_dict_to_msg(data_dict, msg_type):
    msg = msg_type()
    set_message_fields(msg, data_dict)
    return msg


def set_nested_value(data: Any, path: List[str], value: float) -> None:
    """设置嵌套字典或对象中的值

    Args:
        data: 要修改的数据对象
        path: 路径列表，如 ['motor_cmd', '1', 'q'] 表示 motor_cmd[1].q
        value: 要设置的值
    """
    current = data

    # 遍历到倒数第二个路径组件
    for i, key in enumerate(path[:-1]):
        if key.isdigit():
            # 如果是数字，作为数组索引处理
            idx = int(key)
            if isinstance(current, (list, tuple, np.ndarray)):
                current = current[idx]
            else:
                raise ValueError(f"Cannot index non-sequence with {key}")
        else:
            # 否则作为属性处理
            if hasattr(current, "__slots__"):
                # ROS 消息对象
                attr = f"_{key}" if f"_{key}" in current.__slots__ else key
                current = getattr(current, attr)
            elif isinstance(current, dict):
                # 字典
                current = current[key]
            else:
                raise ValueError(f"Cannot access attribute {key} of {type(current)}")

    # 处理最后一个路径组件
    last = path[-1]
    if last.isdigit():
        # 数组索引赋值
        idx = int(last)
        if isinstance(current, (list, tuple)):
            current[idx] = value
        elif isinstance(current, np.ndarray):
            current[idx] = value
        else:
            raise ValueError(f"Cannot index non-sequence with {last}")
    else:
        # 属性赋值
        if hasattr(current, "__slots__"):
            # ROS 消息对象
            attr = f"_{last}" if f"_{last}" in current.__slots__ else last
            setattr(current, attr, value)
        elif isinstance(current, dict):
            # 字典
            current[last] = value
        else:
            raise ValueError(f"Cannot set attribute {last} of {type(current)}")


def generate_incremental_filename(file_path: str, suffix: str = "_modified") -> str:
    """生成递增的文件名

    参数:
        file_path: 原始文件路径
        suffix: 要添加的后缀，默认为"_modified"

    返回:
        新的文件路径，带有递增的后缀
    """
    # 处理文件扩展名
    base_file = file_path
    file_ext = ""

    # 查找最后一个点，提取扩展名
    last_dot = base_file.rfind(".")
    if last_dot > 0:  # 确保找到了点且不是在开头
        file_ext = base_file[last_dot:]
        base_name = base_file[:last_dot]
    else:
        base_name = base_file

    # 检查是否已经有指定后缀
    pattern = f"{re.escape(suffix)}(\\d*)$"
    match = re.search(pattern, base_name)

    if match:
        # 已经有后缀
        num_str = match.group(1)
        if num_str:  # 如果有数字
            num = int(num_str) + 1
            new_base_name = re.sub(pattern, f"{suffix}{num}", base_name)
        else:  # 如果只有后缀没有数字
            new_base_name = f"{base_name}1"
    else:
        # 没有后缀
        new_base_name = f"{base_name}{suffix}"

    return f"{new_base_name}{file_ext}"
