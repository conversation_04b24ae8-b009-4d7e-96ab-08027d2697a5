'''metric_impl_demo.py'''

import numpy as np

from utils import Utils
from utils import metric_params


class MetricBase:
    def __init__(self, manager):
        self.manager = manager
        self._passed = True
        self._details = []
    def execute(self) -> dict:
        """执行指标并返回标准化结果"""
        try:
            self._evaluate()
        except Exception as e:
            self._passed = True
            self._details.append({"error": f"执行时发生异常: {str(e)}"})
        return {
            "passed": self._passed,
            "details": self._details if not self._passed else [],
            "params": self._get_params(),
        }

    def _evaluate(self):
        """子类必须实现的计算逻辑"""
        raise NotImplementedError

    def _get_params(self):
        """获取参数（假设参数由装饰器处理）"""
        return getattr(self, "params", {})

class CogInSupportAreaMetric(MetricBase):
    @metric_params
    def _evaluate(
        self,
        support_area_margin=0.005,
        contact_force_threshold=15.0
    ):
        current_index = (self.manager.current_step - 1) % self.manager.max_steps
        com_xy = self.manager.np_sim_com[current_index][:2]

        # Check support polygon validity
        support_valid = self.manager.check_support_polygon_vertices(contact_force_threshold)
        if not support_valid:
            self._passed = False
            self._details.append({
                "error": "无法计算支撑多边形：接触力不足或无有效接触点",
                "contact_force_threshold": contact_force_threshold
            })
        else:
            distance = Utils.distance_to_polygon(
                com_xy,
                self.manager.support_polygon_vertices
            )
            if distance > -support_area_margin:
                self._passed = False
                self._details.append({
                    "error": "质心超出支撑区域",
                    "distance": distance,
                    "support_area_margin": support_area_margin,
                    "com_xy": com_xy.tolist(),
                    "polygon_vertices": self.manager.support_polygon_vertices.tolist()
                })

class ZmpInSupportPolygonMetric(MetricBase):
    @metric_params
    def _evaluate(
        self,
        support_area_margin=0.005,
        contact_force_threshold=15.0
    ):
        current_index = (self.manager.current_step - 1) % self.manager.max_steps
        com_xy = self.manager.np_sim_com[current_index][:2]

        support_polygon_valid = self.manager.check_support_polygon_vertices(
            contact_force_threshold
        )
        if not support_polygon_valid:
            self._passed = False
            self._details.append({
                "error": "无法计算支撑多边形：接触力不足或无有效接触点",
                "contact_force_threshold": contact_force_threshold
            })

        else:
            distance = Utils.distance_to_polygon(
                com_xy,
                self.manager.support_polygon_vertices
            )
            if distance > -support_area_margin:
                self._passed = False
                self._details.append({
                    "error": "质心超出支撑区域",
                    "distance": distance,
                    "support_area_margin": support_area_margin,
                    "com_xy": com_xy.tolist(),
                    "polygon_vertices": self.manager.support_polygon_vertices.tolist()
                })

class BalanceInAirMetric(MetricBase):
    @metric_params
    def _evaluate(
        self,
        flip_type: str,
        secondary_angle_threshold: float
    ):
        axis_map = {"front_back":1, "side":0, "in_place":2}
        if flip_type not in axis_map:
            self._details.append({
                "error": "无效的空翻类型",
                "valid_types": list(axis_map.keys())
            })
            self._passed = False
        else:
            main_axis = axis_map[flip_type]
            angular_velocities = self.manager.np_sim_angular_vel[:self.manager.current_step]
            total_rotation = np.sum(angular_velocities * self.manager.timestep, axis=0)

            for axis in [0,1,2]:
                if axis == main_axis: continue
                rotation = total_rotation[axis]
                if abs(rotation) > secondary_angle_threshold:
                    self._passed = False
                    self._details.append({
                        "error": "次要轴旋转超限",
                        "axis_index": axis,
                        "actual_rotation": round(rotation,4),
                        "threshold": secondary_angle_threshold
                    })

class SelfContactInAirMetric(MetricBase):
    @metric_params
    def _evaluate(self, contact_force_threshold=1.0):

        for step in range(self.manager.current_step):
            index = step % self.manager.max_steps
            if not self.manager.is_in_air[index]:
                continue

            # 遍历该时间步的所有接触点
            for contact_info in self.manager.contacts[index]:
                force_magnitude = contact_info["magnitude"]
                # 判断是否超过阈值
                if force_magnitude < contact_force_threshold:
                    continue
                self._passed = False
                geom1_name, geom2_name = contact_info["geom_pair_name"]
                self._details.append(
                    {
                        "error": "非足端接触力超标",
                        "step": step,
                        "geom_pair": (geom1_name, geom2_name),
                        "force_magnitude": round(force_magnitude, 4),
                        "threshold": contact_force_threshold,
                    }
                )


class FlipHeightMetric(MetricBase):
    @metric_params
    def _evaluate(self, max_height, min_height):

        for step in range(self.manager.current_step):
            index = step % self.manager.max_steps
            if not self.manager.is_in_air[index]:
                continue
            # 获取当前质心高度（Z轴坐标）
            current_height = self.manager.np_sim_com[index][2]
            if not (min_height <= current_height and current_height <= max_height):
                self._passed = False
                self._details.append(
                    {
                        "step": step,
                        "current_height": current_height,
                        "min_height": min_height,
                        "max_height": max_height,
                        "exceeded": "上限" if current_height > max_height else "下限",
                    }
                )
                break

class ContactsWhenLandingMetric(MetricBase):
    @metric_params
    def _evaluate(self, max_contact_force=1.0):

        # 找到落地时间
        landing_step = 0
        foot_ids = Utils.get_foot_ids(self.manager.initial_model)
        for step in range(1, self.manager.current_step):
            if not self.manager.is_in_air[step - 1] or self.manager.is_in_air[step]:
                continue
            landing_step = step
            break

        for step in range(landing_step, self.manager.current_step):
            index = step % self.manager.max_steps
            if not self.manager.is_in_air[index]:
                continue

            # 遍历该时间步的所有接触点
            for contact_info in self.manager.contacts[index]:
                geom1, geom2 = contact_info["geom_pair_id"]
                force_magnitude = contact_info["magnitude"]

                # 跳过足端接触
                if geom1 in foot_ids or geom2 in foot_ids:
                    continue

                # 判断是否超过阈值
                if force_magnitude < max_contact_force:
                    continue

                self._passed = False
                geom1_name, geom2_name = contact_info["geom_pair_name"]
                self._details.append(
                    {
                        "step": step,
                        "geom_pair": (geom1_name, geom2_name),
                        "force_magnitude": force_magnitude,
                        "threshold": max_contact_force,
                    }
                )
                break
            if not self._passed:
                break

class EnergyConsumptionMetric(MetricBase):
    @metric_params
    def _evaluate(self, max_energy_consumption=100.0):
        tau = self.manager.np_sim_tau[:self.manager.current_step]
        dq = self.manager.np_sim_dq[:self.manager.current_step]
        power_per_step = np.sum(tau * dq, axis=1)
        total_energy = np.sum(power_per_step) * self.manager.timestep
        self._passed = total_energy <= max_energy_consumption
        self._details.append({
            "total_energy": total_energy,
        })
