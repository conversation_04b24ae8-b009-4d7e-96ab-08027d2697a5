#!/usr/bin/env bash
EXEC_ROOT=$(pwd)
# === 全局变量 =================================
WS_DIR=/root/vitasim_cloud                    # 工作目录
CONFIG_FILE="$WS_DIR/offscreen/metrics.yaml"  # 必选：配置文件
OUTPUT_DIR="$WS_DIR/output"                   # 默认输出目录
LOOP_TYPE="close"                             # 必选：仿真类型
BAG_FILE=""                                   # 必选：MCAP文件
VERBOSE=0                                     # 详细模式
TIME_OUT=60                                   # 默认超时
METRIC_IMPL="$WS_DIR/offscreen/metric_impl_demo.py" # 必选：metric实现文件

# === 帮助信息 =================================
show_help() {
  echo "用法: $0 [选项]"
  echo "必选参数:"
  echo "  -c, --config      <文件路径>  配置文件 (如metrics.yaml)"
  echo "  -p, --py_metric   <文件路径>  metric实现文件 (metric_impl_demo.py)"
  echo "可选参数:"
  echo "  -b, --bagfile     <文件路径>  MCAP数据包 (如rosbag2.mcap)"
  echo "  -l, --loop_type   <文件路径>  仿真类型, 可选值: open, close, 默认闭环"
  echo "  -t, --timeout     <文件路径>  闭环仿真算法超时时间 (默认: 60秒)"
  echo "  -o, --output_dir  <目录路径>  输出目录 (默认: $OUTPUT_DIR)"
  echo "  -v, --verbose     启用详细输出"
  echo "  -h, --help        显示此帮助"
  exit 0
}

# === 参数解析 =================================
parse_args() {
  OPTS=$(getopt -o c:p:b:l:t:o:vh --long config:,py_metric:,bagfile:,loop_type:,time_out:,output_dir:,env:,verbose,help -n "$0" -- "$@") || show_help
  eval set -- "$OPTS"

  while true; do
    case "$1" in
      -c|--config)        CONFIG_FILE="$2"; shift 2 ;;
      -p|--py_metric)     METRIC_IMPL="$2"; shift 2 ;;
      -b|--bagfile)       BAG_FILE="$2"; shift 2 ;;
      -l|--loop_type)     LOOP_TYPE="$2"; shift 2 ;;
      -t|--timout)        TIME_OUT="$2"; shift 2 ;;
      -o|--output_dir)    OUTPUT_DIR="$2"; shift 2 ;;
      -v|--verbose)       VERBOSE=1; shift ;;
      -h|--help)          show_help ;;
      --)                 shift; break ;;
      *)                  show_help ;;
    esac
  done

  OUTPUT_DIR=$EXEC_ROOT/$OUTPUT_DIR

  # 校验必选参数
  if [ -z "$CONFIG_FILE" ]; then
    echo "❌ 错误：-c/--config是必选参数!" >&2
    show_help
  fi
}

# === 环境准备 =================================
setup_env() {
  cd $WS_DIR
  source install/setup.bash
  cd $WS_DIR/offscreen

  mkdir -p "$OUTPUT_DIR" || {
    echo "❌ 错误：无法创建输出目录 - $OUTPUT_DIR" >&2
    exit 1
  }
  rm -rf $OUTPUT_DIR/*
  VERSION=$(basename "$OUTPUT_DIR")
  echo '{
      "file_format_version" : "1.0.0",
      "ICD" : {
          "library_path" : "libEGL_nvidia.so.0"
      }
  }' > /usr/share/glvnd/egl_vendor.d/10_nvidia.json
  export MUJOCO_GL=EGL

  if [ -d "/mnt/ramdisk" ]; then
    rm -rf /mnt/ramdisk/*
  fi
  echo "check /mnt/ramdisk"
  ll -a "/mnt/ramdisk"

}

# === 执行命令 =================================
execute() {
  local log_dir="${OUTPUT_DIR}/logs"
  local log_file="${log_dir}/$(date +%Y%m%d-%H%M%S)_sim.log"
  mkdir -p "$log_dir"

  # 校验文件/目录
  for file in "$CONFIG_FILE" "$METRIC_IMPL"; do
    if [ ! -f "$file" ]; then
      echo "❌ 错误：文件不存在 - $file" >&2
      exit 1
    fi
  done

  echo "🚀 启动任务 - 配置: $CONFIG_FILE | 评测实现: $METRIC_IMPL | 任务类型: $LOOP_TYPE | ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
  echo "📁 输出目录: $OUTPUT_DIR | 日志: $log_file"
  echo "----------------------------------------"

  cp $METRIC_IMPL $WS_DIR/offscreen

  # 启动录制命令
  nohup ros2 run vita_flow record > $OUTPUT_DIR/record.out 2>&1 &
  RECORD_PID=$!
  sleep 1


  ros2 service call /control_recording sim_msg/srv/Control "{command: "start", version: "$VERSION"}" >> "$log_file"

  # 执行命令模板
  uv run --no-sync main.py \
  -m $WS_DIR/models/vita00/scene.xml \
  -o $OUTPUT_DIR \
  -c $CONFIG_FILE \
  -l $LOOP_TYPE \
  -t $TIME_OUT | tee "$log_file"

  ros2 service call /control_recording sim_msg/srv/Control "{command: "stop", version: "$VERSION"}" >> "$log_file"

  MAX_TIMEOUT=15
  start_time=$(date +%s)
  while true; do
      current_time=$(date +%s)
      elapsed=$((current_time - start_time))

      last_line=$(tail -n 5 "$OUTPUT_DIR/record.out" 2>/dev/null)
      if [[ "$last_line" =~ ^\[INFO\].*\[record\]:\ Recording\ stopped\ successfully ]]; then
          echo "Recording stopped successfully detected"
          break
      elif [ $elapsed -ge $MAX_TIMEOUT ]; then
          echo "❌ 超时错误：未在$MAX_TIMEOUT秒内检测到停止记录日志!" >&2
      fi
      sleep 1
  done

  ps -ef | grep "vita_flow/lib/vita_flow/record" | grep -v grep | awk '{print $2}' | xargs kill -2

  MAX_CHECK_TIME=30
  CHECK_INTERVAL=2
  FOUND=0

  start_time=$(date +%s)
  while [ $(( $(date +%s) - start_time )) -lt $MAX_CHECK_TIME ]; do
      # Find the latest recording directory
      RECORD_DIR=$(ls -td /mnt/ramdisk/recording_* 2>/dev/null | head -1)
      if [ -n "$RECORD_DIR" ] && [ -f "$RECORD_DIR/metadata.yaml" ]; then
          FOUND=1
          break
      fi
      sleep $CHECK_INTERVAL
  done

  cp -r /mnt/ramdisk/* $OUTPUT_DIR

  ps -ef | grep "vita_flow/lib/vita_flow/record" | grep -v grep | awk '{print $2}' | xargs kill -9

  local status=$?
  if [ $status -eq 0 ]; then
      echo -e "\n✅ 任务成功！结果保存在 $OUTPUT_DIR"
  else
      echo -e "\n❌ 任务失败！查看日志: $log_file" >&2
      exit $status
  fi
}

# === 主流程 =================================
parse_args "$@"
setup_env
execute
