import cv2
import mujoco
import mujoco.viewer


def main():
    # 1. 加载模型与初始化仿真环境
    model_path = "/home/<USER>/Vita/vitamin_sim/src/robot/vita00/scene.xml"  # 替换为实际的 XML 路径
    model = mujoco.MjModel.from_xml_path(model_path)
    data = mujoco.MjData(model)

    # 创建离屏渲染上下文
    width, height = 640, 480  # 根据需要设置分辨率
    renderer = mujoco.Renderer(model, height=height, width=width)

    # 2. 定义采集单帧的函数
    def capture_frame():
        # 更新场景
        renderer.update_scene(data)
        # 渲染并获取图像
        pixels = renderer.render()
        return pixels

    # 3. 设置 OpenCV 视频写入器
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")
    video_path = "simulation_recording.mp4"
    video_writer = cv2.VideoWriter(video_path, fourcc, 30, (width, height))

    # 4. 开始仿真与录屏循环
    num_steps = 1000  # 根据需求设置仿真步数或时间
    for step in range(num_steps):
        mujoco.mj_step(model, data)  # 执行仿真一步
        # 采集当前帧
        frame = capture_frame()
        # 将帧写入视频
        video_writer.write(frame)
        # （可选）在此处记录状态数据，比如保存 data 中的关键信息

    # 5. 释放资源，保存视频
    video_writer.release()
    print(f"录屏完成，视频保存到：{video_path}")


if __name__ == "__main__":
    main()
