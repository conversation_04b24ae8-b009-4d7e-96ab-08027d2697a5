import unittest

from simulation import OffscreenSimulator


class TestOffscreenSimulator(unittest.TestCase):
    def setUp(self):
        # 使用一个测试用的模型路径（请替换成实际可用的 XML 文件路径）
        self.sim = OffscreenSimulator(
            model_path="/home/<USER>/Vita/vitamin_sim/src/robot/vita00/scene.xml",
            width=640,
            height=480,
        )

    def test_capture_frame(self):
        frame = self.sim.capture_frame()
        # 检查帧的尺寸是否正确（假设返回的 frame shape 为 (高度, 宽度, 3)）
        self.assertEqual(frame.shape, (480, 640, 3))


if __name__ == "__main__":
    unittest.main()
