# close_loop_node.py
import queue
import logging
import math
import time
import rclpy
from rclpy.node import Node
from rclpy.qos import DurabilityPolicy, ReliabilityPolicy
from rclpy.qos import QoSProfile
from lowlevel_msg.msg import LowCmd, LowState
from sim_msg.srv import Control
import threading


class CloseLoopSubNode(Node):
    def __init__(self, num_joints: int):
        super().__init__("close_loop_sub_node")
        self.num_joints = num_joints
        self.last_ctrl = []
        self.last_ctrl_time = self.get_clock().now()
        self.first_cmd_arrived = False
        # ROS Subscription
        qos = QoSProfile(depth=100)
        qos.reliability = ReliabilityPolicy.RELIABLE
        self.create_subscription(LowCmd, "/rl_lowcmd", self.control_callback, qos)

        self.is_recording = False

    def control_callback(self, msg: LowCmd):
        """Update control signals on new message"""
        if not self.first_cmd_arrived:
            self.first_cmd_arrived = True
        self.last_ctrl = msg.motor_cmd
        self.last_ctrl_time = self.get_clock().now()

    def get_current_ctrl(self):
        """Get latest control signal (thread-safe)"""
        return self.last_ctrl

    def get_last_time(self):
        """Get timestamp of last received command"""
        return self.last_ctrl_time

class CloseLoopPubNode(Node):
    def __init__(self, num_joints: int):
        super().__init__("close_loop_pub_node")
        self.num_joints = num_joints
        self.state_msg = LowState()
        self.sensordata_queue = queue.Queue(maxsize=3)
        self.state_lock = threading.Lock()

        # 设置 publisher 的 QoS
        qos = QoSProfile(
            depth=50,
            reliability=ReliabilityPolicy.RELIABLE,
            durability=DurabilityPolicy.VOLATILE,
        )
        self.state_msg_pub = self.create_publisher(LowState, "/rt/lowstate", qos)

        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self._update_state_loop)
        self.update_thread.daemon = True
        self.update_thread.start()

    def enqueue_sensordata(self, sensordata):
        """异步提交最新仿真数据"""
        try:
            self.sensordata_queue.put_nowait(sensordata.copy())
        except queue.Full:
            self.get_logger().warning("State data queue is full, dropping oldest data")

    def _update_state_loop(self):
        """后台线程持续更新状态数据"""
        while rclpy.ok():
            if not self.sensordata_queue.empty():
                sensordata = self.sensordata_queue.get()
                for i in range(self.num_joints):
                    self.state_msg.motor_state[i].q = sensordata[i]
                    self.state_msg.motor_state[i].dq = sensordata[
                        i + self.num_joints
                    ]
                    self.state_msg.motor_state[i].tau_est = sensordata[
                        i + 2 * self.num_joints
                    ]

                data_offset = 3 * self.num_joints
                self.state_msg.imu_state.quaternion[:] = sensordata[
                    data_offset : data_offset + 4
                ]
                # Convert gyroscope data from rad/s to deg/s (multiply by 180/π)
                # https://mujoco.readthedocs.io/en/latest/overview.html#units-are-unspecified
                gyro_data = sensordata[data_offset + 4 : data_offset + 7]
                self.state_msg.imu_state.gyroscope[:] = [g * 180.0 / math.pi for g in gyro_data]

                # Convert accelerometer data from m/s² to g-units (divide by 9.81)
                # https://mujoco.readthedocs.io/en/latest/overview.html#units-are-unspecified
                accel_data = sensordata[data_offset + 7 : data_offset + 10]
                self.state_msg.imu_state.accelerometer[:] = [a / 9.81 for a in accel_data]
                self.sensordata_queue.task_done()
                self.state_msg_pub.publish(self.state_msg)
            time.sleep(0.0001)
