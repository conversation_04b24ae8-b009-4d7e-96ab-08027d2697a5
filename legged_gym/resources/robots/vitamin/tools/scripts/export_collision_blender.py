import bpy
import math  # 添加math模块导入
from math import radians
from mathutils import Euler

# --------------------------
# 可配置参数（根据需求修改）
# --------------------------
CONFIG = {
    "collision_prefix": "collision_",  # 碰撞体命名前缀（必须以此前缀开头）
    "output_path": "~/Documents/test_output/collisions.urdf",  # 输出路径
    "adjust_coordinate_system": False,  # 是否调整坐标系
    "euler_order": 'XYZ',  # 旋转顺序（与Blender对象一致）
    "cylinder_tolerance": 0.001,  # 圆柱体X/Z轴尺寸容差
    "sphere_tolerance": 0.001   # 球体维度容差
}

# --------------------------
# 核心功能函数
# --------------------------
def is_collision_object(obj):
    """判断是否为目标碰撞体（名称符合collision_{name}_{geometry_type}格式）"""
    if obj.type != 'MESH':
        return False
    name = obj.name
    # 必须以collision_开头，且至少有3部分（collision_ + 名称 + 类型）
    parts = name.split("_")
    if len(parts) < 3 or not name.startswith(CONFIG["collision_prefix"]):
        return False
    return True

def get_geometry_type(obj):
    """从名称中解析几何类型（最后一个下划线后的部分）"""
    name = obj.name
    parts = name.split("_")
    # 最后一个部分为几何类型（需是box/cylinder/sphere）
    geometry_type = parts[-1].lower()
    if geometry_type in ["box", "cylinder", "sphere"]:
        return geometry_type
    # 未识别到有效类型时返回None（后续跳过）
    return None

def get_parent_link(obj):
    """获取碰撞体的父链接（默认base_link）"""
    if obj.parent and obj.parent.type == 'LINK':
        return obj.parent.name
    return "base_link"

def adjust_rotation_for_urdf(euler_rad):
    """调整旋转以适配URDF坐标系"""
    if not CONFIG["adjust_coordinate_system"]:
        return euler_rad
    
    # 将欧拉角转换为4x4旋转矩阵
    rot_matrix = euler_rad.to_matrix().to_4x4()
    
    adjust_rot = Euler((0, 0, 0), CONFIG["euler_order"])
    adjust_matrix = adjust_rot.to_matrix().to_4x4()
    
    # 应用调整（用户旋转 × 调整旋转）
    combined_matrix = rot_matrix @ adjust_matrix
    combined_euler = combined_matrix.to_euler(CONFIG["euler_order"])
    return combined_euler

def export_collisions():
    # 筛选碰撞体对象
    collision_objs = [obj for obj in bpy.context.scene.objects if is_collision_object(obj)]
    if not collision_objs:
        print("错误：未找到任何碰撞体对象！请检查命名规则（collision_{name}_{geometry_type}）。")
        return

    xml_lines = ["<robot name='robot'>\n"]
    for obj in collision_objs:
        # 解析几何类型
        geometry_type = get_geometry_type(obj)
        if not geometry_type:
            print(f"警告：对象 {obj.name} 命名不符合规则（类型需为box/cylinder/sphere），跳过。")
            continue
        
        parent_link = get_parent_link(obj)
        
        # 获取局部位置（相对于父链接）
        origin_xyz = (round(obj.location.x, 6), round(obj.location.y, 6), round(obj.location.z, 6))
        
        # 获取旋转（调整后的欧拉角）
        euler_rad = Euler(obj.rotation_euler, CONFIG["euler_order"])
        adjusted_euler = adjust_rotation_for_urdf(euler_rad)
        origin_rpy = (
            round(adjusted_euler.x, 6),
            round(adjusted_euler.y, 6),
            round(adjusted_euler.z, 6)
        )
        
        # 根据类型提取尺寸参数
        if geometry_type == "box":
            size = (
                round(obj.dimensions.x, 6),
                round(obj.dimensions.y, 6),
                round(obj.dimensions.z, 6)
            )
            geom_xml = f"<box size=\"{size[0]} {size[1]} {size[2]}\"/>"
        elif geometry_type == "cylinder":
            radius = round(obj.dimensions.x / 2, 6)  # X轴为直径→半径=直径/2
            length = round(obj.dimensions.z, 6)      # z轴为高度
            geom_xml = f"<cylinder length=\"{length}\" radius=\"{radius}\"/>"
        elif geometry_type == "sphere":
            # 球体三个维度应相等，取平均作为半径
            avg_dim = (obj.dimensions.x + obj.dimensions.y + obj.dimensions.z) / 3
            radius = round(avg_dim / 2, 6)  # 直径→半径
            geom_xml = f"<sphere radius=\"{radius}\"/>"
        
        # 写入碰撞体XML
        xml_lines.append(f"""    <collision name='{obj.name}'>
      <origin rpy="{origin_rpy[0]} {origin_rpy[1]} {origin_rpy[2]}" xyz="{origin_xyz[0]} {origin_xyz[1]} {origin_xyz[2]}"/>
      <geometry>
        {geom_xml}
      </geometry>
    </collision>""")
    xml_lines.append("</robot>")

    # 写入文件（替换~为用户主目录）
    output_path = bpy.path.abspath(CONFIG["output_path"].replace("~", ""))
    with open(output_path, 'w') as f:
        f.write("\n".join(xml_lines))
    
    print(f"成功导出碰撞体至：{output_path}")

# --------------------------
# 执行导出
# --------------------------
export_collisions()