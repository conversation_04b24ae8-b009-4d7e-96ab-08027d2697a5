#!/usr/bin/env python3
import os
import argparse
import numpy as np
import pandas as pd
import rclpy
from rclpy.serialization import serialize_message
from rosidl_runtime_py.utilities import get_message
from lowlevel_msg.msg import LowCmd, MotorCmd
from scipy import interpolate
import rosbag2_py
from rclpy.time import Time
import datetime

# from mcap.writer import Writer
JNT_NUM = 14

class CSVToMCAPConverter:
    def __init__(self, csv_file_path, interp_freq):
        """初始化转换器"""
        self.csv_file_path = csv_file_path
        self.interp_freq = interp_freq
        # 仿真环境的初始姿态 (单位:弧度)
        self.sim_initial_pos = np.array(
            [-0.161, 1.19, -2.76, 0.161, 1.19, -2.76, -0.161, 1.19, -2.76, 0.161, 1.19, -2.76, 0, 0],
            dtype=float
        )
        
        # 目标姿态/动画初始姿态 (单位:弧度)
        self.target_joint_pos = np.array(
            [0.0, 0.76, -1.43, 0.0, 0.76, -1.43, 0.0, 0.76, -1.43, 0.0, 0.76, -1.43, 0, 0], 
            dtype=float
        )
        
        # 关节名称到索引的映射
        self.joint_mapping = {
            'FR_Hip': 0,
            'FR_Thigh': 1,
            'FR_Calf': 2,
            'FL_Hip': 3,
            'FL_Thigh': 4,
            'FL_Calf': 5,
            'RR_Hip': 6,
            'RR_Thigh': 7,
            'RR_Calf': 8,
            'RL_Hip': 9,
            'RL_Thigh': 10,
            'RL_Calf': 11,
            'Head_X': 12,
            'Head_Z': 13,
        }
        
        # 控制参数
        self.dt = 0.002  # 2ms间隔，对应50Hz
        self.transition_steps = 1000  # 过渡阶段步数 (2秒)
        
        # 消息主题
        self.topic_name = "/sim/lowcmd"
        
        # 输出文件路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.output_file = os.path.join(script_dir, "joint_angles.mcap")
        
        print(f"CSV文件: {self.csv_file_path}")
        print(f"输出MCAP文件: {self.output_file}")
    
    def read_and_interpolate_csv(self):
        """读取CSV文件并进行插值"""
        print("读取CSV文件并进行插值...")
        
        # 读取CSV文件
        df = pd.read_csv(self.csv_file_path)
        
        # 提取关节角度数据
        joint_data = {}
        joint_names = list(self.joint_mapping.keys())
        
        # 确保目标关节位置数组长度与关节数量匹配
        if len(self.target_joint_pos) != len(joint_names):
            print(f"警告: 目标关节位置数组长度({len(self.target_joint_pos)})与关节数量({len(joint_names)})不匹配")
        
        # 对每个关节列加上对应的目标位置值
        for i, joint_name in enumerate(joint_names):
            if joint_name in df.columns:
                # 加上目标位置值
                joint_data[joint_name] = df[joint_name].values + self.target_joint_pos[i]
            else:
                print(f"警告: CSV文件中未找到关节 {joint_name}，使用目标位置值替代")
                # 直接使用目标位置值
                joint_data[joint_name] = np.full(len(df), self.target_joint_pos[i])
        
        # 创建原始时间点 (25Hz)
        original_time = np.arange(0, len(df)) * (1.0 / 25.0)
        
        # 创建插值后的时间点 (用户指定频率)
        end_time = original_time[-1]
        interp_time = np.arange(0, end_time, 1.0 / self.interp_freq)
        
        # 对每个关节数据进行插值
        interp_joint_data = {}

        for joint_name, data in joint_data.items():
            # 使用三次样条插值
            f = interpolate.interp1d(original_time, data, kind='cubic', bounds_error=False, fill_value="extrapolate")
            interp_joint_data[joint_name] = f(interp_time)
        
        # 将插值后的数据转换为numpy数组，形状为 [time_steps, JNT_NUM]
        num_steps = len(interp_time)
        interpolated_data = np.zeros((num_steps, JNT_NUM))
        
        for joint_name, joint_idx in self.joint_mapping.items():
            interpolated_data[:, joint_idx] = interp_joint_data[joint_name]
        
        print(f"插值完成: 从{len(df)}帧(25Hz)插值到{num_steps}帧({self.interp_freq}Hz)")
        return interpolated_data
    
    def create_transition_data(self):
        """创建从初始姿态到目标姿态的过渡阶段数据"""
        print("创建过渡阶段数据...")
        
        # 创建过渡阶段数据，形状为 [transition_steps, JNT_NUM]
        transition_data = np.zeros((self.transition_steps, JNT_NUM))
        
        # 对每一步进行线性插值
        for step in range(self.transition_steps):
            ratio = step / (self.transition_steps - 1)  # 从0到1的比例
            transition_data[step] = (1 - ratio) * self.sim_initial_pos + ratio * self.target_joint_pos
        
        print(f"过渡阶段数据创建完成: {self.transition_steps}帧")
        return transition_data
    
    def create_lowcmd_message(self, joint_positions):
        """创建LowCmd消息"""
        cmd = LowCmd()
        cmd.head = [0xFE, 0xEF]
        cmd.level_flag = 0xFF
        
        # 为JNT_NUM个关节创建MotorCmd
        for i in range(JNT_NUM):
            cmd.motor_cmd[i].q = float(joint_positions[i])
            cmd.motor_cmd[i].kp = 30.0    # 位置增益
            cmd.motor_cmd[i].dq = 0.0
            cmd.motor_cmd[i].kd = 1.0     # 速度增益
            cmd.motor_cmd[i].tau = 0.0
        
        return cmd
    
    def write_to_mcap(self, all_joint_data):
        """将关节数据写入MCAP文件"""
        print("开始写入MCAP文件...")
        
        # 初始化ROS2
        rclpy.init()
        
        # 创建MCAP写入器
        writer = rosbag2_py.SequentialWriter()
        
        # 创建存储选项
        storage_options = rosbag2_py._storage.StorageOptions(
            uri=self.output_file,
            storage_id='mcap'
        )
        
        # 创建转换选项
        converter_options = rosbag2_py.ConverterOptions(
            input_serialization_format='cdr',
            output_serialization_format='cdr'
        )
        
        # 打开存储
        writer.open(storage_options, converter_options)
        
        # 获取消息类型
        topic_type = 'lowlevel_msg/msg/LowCmd'
        
        # 创建主题元数据
        topic_info = rosbag2_py._storage.TopicMetadata(
            name=self.topic_name,
            type=topic_type,
            serialization_format='cdr',
            offered_qos_profiles=''
        )
        
        # 注册主题（关键修复）
        writer.create_topic(topic_info)
        
        # 获取消息类型
        msg_type = get_message(topic_type)
        
        # 写入每一帧数据
        num_frames = len(all_joint_data)
        start_time = Time().to_msg()
        start_time_ns = start_time.sec * 1_000_000_000 + start_time.nanosec
        
        for i, joint_positions in enumerate(all_joint_data):
            # 创建消息
            msg = self.create_lowcmd_message(joint_positions)
            # 序列化消息
            serialized_msg = serialize_message(msg)
            
            # 计算时间戳 (每帧增加dt秒)
            timestamp_ns = start_time_ns + int(i * self.dt * 1_000_000_000)
            
            # 写入消息
            writer.write(
                self.topic_name,
                serialized_msg,
                timestamp_ns
            )
        
        # 关闭写入器
        del writer
        
        print(f"MCAP文件写入完成: {self.output_file}")
    
    def convert(self):
        """执行转换过程"""
        print("开始CSV到MCAP的转换过程...")
        
        # 1. 读取和插值CSV数据
        csv_data = self.read_and_interpolate_csv()
        
        # 2. 创建过渡阶段数据
        transition_data = self.create_transition_data()
        
        # 3. 合并数据
        all_joint_data = np.vstack([transition_data, csv_data])
        
        # 4. 写入MCAP文件
        self.write_to_mcap(all_joint_data)
        
        print("转换完成!")
        return self.output_file

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--csv_file", help="输入CSV文件路径")
    parser.add_argument("--interp_freq", type=float, default=50.0, 
                        help="插值频率(Hz)，默认50Hz")  # 新增：插值频率参数
    
    args = parser.parse_args()
    csv_file_path = args.csv_file
        
    # 创建转换器并执行转换
    converter = CSVToMCAPConverter(csv_file_path, args.interp_freq)  # 传递插值频率
    output_file = converter.convert()
    
    print(f"转换完成! MCAP文件已保存到: {output_file}")

if __name__ == "__main__":
    main()