import xml.etree.ElementTree as ET
import csv
import sys

def parse_joint_xml(xml_path, csv_path):
    """
    解析URDF/SDF XML文件，提取关节轴和限位参数到CSV
    
    参数:
        xml_path: 输入XML文件路径
        csv_path: 输出CSV文件路径
    """
    try:
        tree = ET.parse(xml_path)
    except FileNotFoundError:
        print(f"错误：文件 {xml_path} 未找到", sys.stderr)
        return
    except ET.ParseError:
        print(f"错误：XML文件 {xml_path} 解析失败", sys.stderr)
        return

    root = tree.getroot()
    joints = root.findall(".//joint")  # 查找所有joint标签

    if not joints:
        print("警告：未找到任何joint标签", sys.stderr)
        return

    # 初始化CSV写入
    with open(csv_path, 'w', newline='') as csvfile:
        fieldnames = ['joint_name', 'axis_xyz', 'limit_lower', 'limit_upper', 'limit_effort', 'limit_velocity']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for joint in joints:
            joint_info = {}
            
            # 提取关节名称
            joint_name = joint.get('name')
            if not joint_name:
                print(f"警告：跳过无名joint标签", sys.stderr)
                continue
            joint_info['joint_name'] = joint_name

            # 提取轴信息
            axis_elem = joint.find('./axis')
            if axis_elem is None:
                print(f"警告：关节 {joint_name} 缺少<axis>标签，跳过", sys.stderr)
                continue
            axis_xyz = axis_elem.get('xyz')
            if not axis_xyz:
                print(f"警告：关节 {joint_name} 的<axis>缺少xyz属性，跳过", sys.stderr)
                continue
            joint_info['axis_xyz'] = axis_xyz.strip()

            # 提取限位信息
            limit_elem = joint.find('./limit')
            if limit_elem is None:
                print(f"警告：关节 {joint_name} 缺少<limit>标签，跳过", sys.stderr)
                continue
            
            # 强制转换为浮点数，处理可能的科学计数法
            try:
                joint_info['limit_lower'] = float(limit_elem.get('lower', 0))
                joint_info['limit_upper'] = float(limit_elem.get('upper', 0))
                joint_info['limit_effort'] = float(limit_elem.get('effort', 0))
                joint_info['limit_velocity'] = float(limit_elem.get('velocity', 0))
            except ValueError:
                print(f"警告：关节 {joint_name} 的限位值格式错误，跳过", sys.stderr)
                continue

            # 写入CSV（保留3位小数）
            writer.writerow({
                'joint_name': joint_name,
                'axis_xyz': axis_xyz,
                'limit_lower': f"{joint_info['limit_lower']:.3f}",
                'limit_upper': f"{joint_info['limit_upper']:.3f}",
                'limit_effort': f"{joint_info['limit_effort']:.1f}",
                'limit_velocity': f"{joint_info['limit_velocity']:.1f}"
            })

    print(f"成功解析 {len(joints)} 个关节，结果保存到 {csv_path}")
def parse_link_inertia(xml_path, csv_path):
    """
    Extract link inertial parameters to CSV
    """
    try:
        tree = ET.parse(xml_path)
    except FileNotFoundError:
        print(f"Error: File {xml_path} not found", file=sys.stderr)
        return
    except ET.ParseError:
        print(f"Error: Failed to parse XML file {xml_path}", file=sys.stderr)
        return

    root = tree.getroot()
    links = root.findall(".//link")  # Find all link elements

    if not links:
        print("Warning: No link tags found", file=sys.stderr)
        return

    # CSV headers
    fieldnames = ['link_name', 'mass', 'ixx', 'ixy', 'ixz', 'iyy', 'iyz', 'izz']

    with open(csv_path, 'w', newline='') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for link in links:
            link_name = link.get('name')
            if not link_name:
                print(f"Warning: Skipping unnamed link", file=sys.stderr)
                continue

            inertial = link.find('inertial')
            if inertial is None:
                print(f"Warning: Link {link_name} has no inertial data", file=sys.stderr)
                continue

            # Parse mass
            mass_elem = inertial.find('mass')
            if mass_elem is None:
                print(f"Warning: Missing mass in link {link_name}", file=sys.stderr)
                continue
            mass = float(mass_elem.get('value', 0))

            # Parse inertia values
            inertia = inertial.find('inertia')
            if inertia is None:
                print(f"Warning: Missing inertia in link {link_name}", file=sys.stderr)
                continue

            inertia_values = {
                'ixx': inertia.get('ixx'),
                'ixy': inertia.get('ixy'),
                'ixz': inertia.get('ixz'),
                'iyy': inertia.get('iyy'),
                'iyz': inertia.get('iyz'),
                'izz': inertia.get('izz')
            }

            # Check for missing values
            if any(v is None for v in inertia_values.values()):
                print(f"Warning: Incomplete inertia data for link {link_name}", file=sys.stderr)
                continue

            # Write to CSV with formatted precision
            writer.writerow({
                'link_name': link_name,
                'mass': f"{mass:.3f}",
                'ixx': f"{float(inertia_values['ixx']):.6f}",
                'ixy': f"{float(inertia_values['ixy']):.6f}",
                'ixz': f"{float(inertia_values['ixz']):.6f}",
                'iyy': f"{float(inertia_values['iyy']):.6f}",
                'iyz': f"{float(inertia_values['iyz']):.6f}",
                'izz': f"{float(inertia_values['izz']):.6f}"
            })

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print(f"Usage: python {sys.argv[0]} <input_xml> <output_joints.csv> <output_inertia.csv>")
        sys.exit(1)
    
    input_xml = sys.argv[1]
    joints_csv = sys.argv[2]
    inertia_csv = sys.argv[3]

    print("Parsing joints...")
    parse_joint_xml(input_xml, joints_csv)

    print("Parsing link inertia...")
    parse_link_inertia(input_xml, inertia_csv)

    print("Done. Results saved to:")
    print(f"  Joints: {joints_csv}")
    print(f"  Inertia: {inertia_csv}")