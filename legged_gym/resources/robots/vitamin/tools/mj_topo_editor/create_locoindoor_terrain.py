#!/usr/bin/env python3

"""MuJoCo 地形编辑工具示例"""

import argparse
from terrain_generator import TerrainGenerator
import numpy as np


def add_slope(tg: TerrainGenerator,
              slope_length: float = 0.75,
              slope_height: float = 0.25,
              slope_width: float = 1.5,
              slope_thickness: float = 0.05,
              start_position: list = [1.0, 0.0, 0.0],
              reverse: bool = False):
    """添加斜坡

    Args:
        tg: 地形生成器实例
        slope_length: 斜坡在地面上的投影长度
        slope_height: 斜坡高度
        slope_width: 斜坡宽度
        slope_thickness: 斜坡厚度
        start_position: 斜坡起点位置 [x, y, z]
        reverse: 是否反向斜坡（从高到低）
    """
    # 避免除零错误
    if slope_length == 0:
         raise ValueError("斜坡长度不能为零")

    # 计算实际斜坡长度（斜面长度）
    actual_length = np.sqrt(slope_length**2 + slope_height**2)
    
    # 计算倾斜角度 (弧度)
    angle = np.arctan2(slope_height, slope_length)
    # 如果是反向斜坡，角度取反
    angle = angle if reverse else -angle

    box_size = [actual_length, slope_width, slope_thickness]

    # 计算斜坡中心位置
    pos_x = slope_length / 2
    pos_z = slope_height / 2
    
    # 如果是反向斜坡，x位置需要调整
    if reverse:
        box_position = [start_position[0] - pos_x,
                        start_position[1],
                        start_position[2] + pos_z]
    else:
        box_position = [start_position[0] + pos_x,
                        start_position[1],
                        start_position[2] + pos_z]

    # 计算旋转 euler 角
    box_euler = [0.0, angle, 0.0]

    # 添加斜坡
    tg.AddBox(position=box_position,
              euler=box_euler,
              size=box_size)
    
    return

def create_loco_indoor(robot: str):
    """创建行走的室内场景"""
    tg = TerrainGenerator(robot)

    # 添加斜坡
    add_slope(tg, 
              slope_length=0.75, 
              slope_height=0.2, 
              slope_width=1.5, 
              start_position=[1.0, 0.0, 0.0])
    
    # 添加不平整地面
    tg.AddRoughGround(init_pos=[1.65, -1, 0.2],
                      euler=[0.0, 0.0, 0.0],
                      nums=[5, 10],
                      box_size=[0.2, 0.2, 0.2],
                      separation=[0.2, 0.2],
                      box_size_rand=[0.01, 0.01, 0.01],
                      box_euler_rand=[0.3, 0.3, 0.3],
                      separation_rand=[0.01, 0.01])
    tg.AddRoughGround(init_pos=[2.55, -1, 0.2],
                      euler=[0.0, 0.0, 0.0],
                      nums=[10, 20],
                      box_size=[0.1, 0.1, 0.2],
                      separation=[0.1, 0.1],
                      box_size_rand=[0.01, 0.01, 0.01],
                      box_euler_rand=[0.3, 0.3, 0.3],
                      separation_rand=[0.01, 0.01])
    tg.AddRoughGround(init_pos=[3.55, -1, 0.2],
                      euler=[0.0, 0.0, 0.0],
                      nums=[20, 40],
                      box_size=[0.05, 0.05, 0.2],
                      separation=[0.05, 0.05],
                      box_size_rand=[0.01, 0.01, 0.01],
                      box_euler_rand=[0.3, 0.3, 0.3],
                      separation_rand=[0.01, 0.01])

    # 添加斜坡
    add_slope(tg, 
              slope_length=0.75, 
              slope_height=0.2, 
              slope_width=1.5, 
              start_position=[5.3, 0.0, 0.0],  # 起点在高处，x位置调整为对称位置
              reverse=True)  # 设置为反向斜坡

    # 添加斜坡
    add_slope(tg,
              slope_length=0.5,
              slope_height=0.34, 
              slope_width=1.5,
              start_position=[6.15, 0.0, 0.0])
    tg.AddBox(position=[6.9, 0.0, 0.17],
              euler=[0.0, 0.0, 0.0],
              size=[0.5, 1.5, 0.34])
    add_slope(tg, 
              slope_length=0.5, 
              slope_height=0.34, 
              slope_width=1.5, 
              start_position=[7.65, 0.0, 0.0],
              reverse=True)  # 设置为反向斜坡

    # 添加楼梯
    tg.AddStairs(init_pos=[8.5, 0.0, 0.0],
                 yaw=0.0,
                 width=0.27,
                 height=0.12,
                 length=0.82,
                 stair_nums=5)
    tg.AddBox(position=[10.115, 0.0, 0.3],
              euler=[0.0, 0.0, 0.0],
              size=[0.26, 0.82, 0.6])
    tg.AddStairs(init_pos=[11.73, 0.0, 0.0],
                 yaw=np.pi,
                 width=0.27,
                 height=0.12,
                 length=0.82,
                 stair_nums=5)

    # 添加宽楼梯
    tg.AddStairs(init_pos=[8.5, 5.0, 0.0],
                 yaw=0.0,
                 width=0.27,
                 height=0.12,
                 length=2.0,
                 stair_nums=5)
    tg.AddBox(position=[10.115, 5.0, 0.3],
              euler=[0.0, 0.0, 0.0],
              size=[0.26, 2.0, 0.6])
    tg.AddStairs(init_pos=[11.73, 5.0, 0.0],
                 yaw=np.pi,
                 width=0.27,
                 height=0.12,
                 length=2.0,
                 stair_nums=5)

    # 保存场景
    tg.Save()
    print(f"场景已保存到: {tg.output_path}")



def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MuJoCo 地形编辑工具")
    parser.add_argument(
        "--robot", type=str, default="vita00", help="机器人类型 (vita00 或 vita00w)"
    )
    args = parser.parse_args()

    if args.robot not in ["vita00", "vita00w", "vita01"]:
        print(f"错误：不支持的机器人类型 {args.robot}")
        return

    create_loco_indoor(args.robot)


if __name__ == "__main__":
    main()
