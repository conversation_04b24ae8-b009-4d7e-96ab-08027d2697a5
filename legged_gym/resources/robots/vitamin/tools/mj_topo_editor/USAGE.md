# MuJoCo 地形编辑工具使用指南

## 1. 环境准备

### 1.1 依赖安装

已通过 `uv` 包管理器安装必要依赖：

```bash
uv sync
```

### 1.2 文件结构

```
src/
|── robot/
|   |── vita00/
|   |   |── scene.xml # 场景文件
|   |── vita00w/
|       |── scene.xml # 场景文件
tools/
|── mj_topo_editor
    |── terrain_generator.py # 核心功能实现
    |── main.py # 主程序入口
```

## 2. 快速测试

如果你想快速查看工具的效果，可以按以下步骤操作：

```bash
# 1. 生成测试场景（默认为 vita00）
uv run main.py

# 2. 预览生成的场景
# 场景文件位于：src/robot/vita00/scene_terrain.xml
simulate src/robot/vita00/scene_terrain.xml
```

你也可以指定机器人类型：

```bash
uv run main.py --robot vita00w
simulate src/robot/vita00w/scene_terrain.xml
```

生成的场景包含了多种地形示例：

- 基础几何体：斜坡、圆柱体
- 复合地形：普通楼梯、悬空楼梯
- 随机地形：不平整地面
- 高度场：Perlin 噪声地形、VITA logo 地形

## 3. 基本用法

### 3.1 创建地形生成器

```python
from terrain_generator import TerrainGenerator

# 初始化地形生成器
tg = TerrainGenerator()
```

### 3.2 添加基本几何体

```python
# 添加一个长方体
tg.AddBox(
    position=[1.5, 0.0, 0.1],  # [x, y, z]
    euler=[0, 0, 0.0],         # [roll, pitch, yaw]
    size=[1, 1.5, 0.2]         # [length, width, height]
)

# 添加其他几何体
tg.AddGeometry(
    position=[1.5, 0.0, 0.25],
    euler=[0, 0, 0.0],
    size=[1.0, 0.5, 0.5],
    geo_type="cylinder"  # 支持 "plane", "sphere", "capsule", "ellipsoid", "cylinder", "box"
)
```

### 3.3 添加复合地形

#### 楼梯

```python
# 普通楼梯
tg.AddStairs(
    init_pos=[1.0, 4.0, 0.0],  # 起始位置
    yaw=0.0,                   # 偏航角
    width=0.2,                 # 台阶宽度
    height=0.15,               # 台阶高度
    length=1.5,                # 台阶长度
    stair_nums=10              # 台阶数量
)

# 悬空楼梯
tg.AddSuspendStairs(
    init_pos=[1.0, 6.0, 0.0],
    yaw=0.0,
    gap=0.1                    # 间隙高度
)
```

#### 不平整地面

```python
tg.AddRoughGround(
    init_pos=[-2.5, 5.0, 0.0],
    euler=[0, 0, 0.0],
    nums=[10, 8],             # [x方向数量, y方向数量]
    box_size=[0.5, 0.5, 0.5], # 基础方块尺寸
    separation=[0.2, 0.2],    # 间距
    box_size_rand=[0.05, 0.05, 0.05],   # 尺寸随机范围
    box_euler_rand=[0.2, 0.2, 0.2],     # 姿态随机范围
    separation_rand=[0.05, 0.05]         # 间距随机范围
)
```

### 3.4 添加高度场地形

#### Perlin 噪声高度场

```python
tg.AddPerlinHeighField(
    position=[-1.5, 4.0, 0.0],
    size=[2.0, 1.5],          # [width, length]
    height_scale=0.2,         # 最大高度
    negative_height=0.2,      # z轴负方向高度
    smooth=100.0,             # 平滑度
    perlin_octaves=6,         # Perlin 噪声参数
    perlin_persistence=0.5,
    perlin_lacunarity=2.0
)
```

#### 从图片生成高度场

```python
tg.AddHeighFieldFromImage(
    position=[-1.5, 2.0, 0.0],
    size=[2.0, 2.0],
    height_scale=0.02,        # 最大高度
    negative_height=0.1,      # z轴负方向高度
    input_img="./input.png",  # 输入图片路径
    image_scale=[1.0, 1.0],   # 图片缩放比例
    invert_gray=False         # 是否反转灰度
)
```

### 3.5 保存场景

```python
# 保存为 MuJoCo XML 文件
tg.Save()
```

## 4. 注意事项

1. 确保输入的场景文件（scene.xml）存在且格式正确
2. 地形尺寸和位置要考虑机器人的实际尺寸
3. 高度场生成时注意分辨率和性能的平衡
4. 随机地形参数要在合理范围内，避免生成不可行的地形
5. 保存前检查输出路径是否正确
