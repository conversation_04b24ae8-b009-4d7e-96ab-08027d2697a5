---
description: commit message
globs: 
alwaysApply: false
---
---
description: git commit
globs: 
alwaysApply: true
---
# Git Commit Convention

## CRITICAL RULES FOR AI COMMIT GENERATION
[ATTENTION AI: YOU MUST FOLLOW THESE RULES WITHOUT EXCEPTION]

1. TITLE RULES:
   - YOU MUST GENERATE ALL COMMIT TITLES IN ENGLISH
   - YOU MUST NEVER USE CHINESE CHARACTERS IN TITLES
   - YOU MUST USE THE FOLLOWING TRANSLATIONS:
     ```
     重构     -> refactor
     新增     -> feat
     修复     -> fix
     优化     -> perf
     文档     -> docs
     测试     -> test
     ```

2. EXAMPLE TRANSFORMATIONS:
   ```
   ❌ feat: 添加仿真暂停功能
   ✅ feat(sim): add simulation pause functionality

   ❌ refactor: 重构控制器代码
   ✅ refactor(controller): restructure control logic
   ```

## Title Format (REQUIRED)
- MUST be in English only
- MUST follow Conventional Commits specification
- MUST NOT exceed 50 characters
- MUST NOT contain any Chinese characters
- MUST use one of the following types:
  * feat: new feature
  * fix: bug fix
  * docs: documentation changes
  * style: formatting, no code change
  * refactor: code refactoring without changing functionality
  * perf: performance improvements
  * test: adding or testing
  * chore: updating build tasks, package manager configs, etc.
  * ci: changes to CI configuration files and scripts

## Title Pattern
```
<type>(<scope>): <description>
```
- type: 上述类型之一（必需）
- scope: 影响范围（可选），建议使用：
  * sim: 仿真相关
  * controller: 控制器相关
  * robot: 机器人相关
  * core: 核心功能
  * api: API 相关
  * ui: 界面相关
  * docs: 文档相关
  * test: 测试相关
  * deps: 依赖相关
- description: 简短的英文描述（必需）

## Body Format (REQUIRED)
- MUST be in Chinese
- MUST provide detailed explanation of changes
- MUST be written in bullet points
- SHOULD include the motivation for the change

## Simulation-Related Examples

### ✅ Correct Examples:
```
feat(sim): add simulation pause functionality
- 添加仿真暂停功能
- 在控制器中实现暂停状态检查
- 确保暂停时不执行控制命令

feat(controller): implement state machine
- 实现控制器状态机
- 添加各种运动状态的转换逻辑
- 优化状态切换的稳定性

fix(sim): resolve physics timestep issues
- 修复物理引擎时间步长问题
- 优化仿真稳定性
- 添加时间同步机制

perf(controller): optimize control loop
- 优化控制循环性能
- 减少计算延迟
- 提高实时性
```

### ❌ Incorrect Examples:
```
feat: 添加暂停功能
- Added pause functionality
- Improved simulation control

fix: 修复仿真问题
- Fixed simulation issues
- Improved stability

update: some changes
- 做了一些改动
- 优化了一些功能
```

## AI 注意事项
1. 你必须始终使用英文生成 commit title
2. 你必须确保 title 不包含任何中文字符
3. 你必须使用上述翻译对照表进行准确翻译
4. 你必须在 body 部分使用中文详细说明改动

## 通用注意事项
1. title 必须具体，避免模糊的描述如 "update"、"fix"、"change" 等
2. 多个改动应拆分为多个小的提交
3. body 中的中文描述要具体，避免笼统的说明
4. 每个提交都应该是完整的、独立的，能够单独回滚 
