{"configById": {"DogLegSideView.DogLegSideView!2n3c7iq": {"robotType": "vita01", "fullRangePitch": false, "lowStateTopic": "/rt/lowstate", "lowCmdTopic": "/rl_lowcmd"}, "RawMessages!1a537f6": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rl_lowcmd", "fontSize": 12, "expansion": "none"}, "RawMessages!2kwfjxn": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/lowstate", "fontSize": 12, "expansion": "none"}, "Plot!4jizk9q": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[$motor].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[$motor].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[$motor].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[$motor].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[$motor].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].tau_est", "enabled": false, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[$motor].dq", "enabled": false, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "GlobalVariableSliderPanel!3nulfcx": {"sliderProps": {"min": 0, "max": 11, "step": 1}, "globalVariableName": "motor"}, "RawMessages!iyhoq9": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rl_lowcmd.cmd_id", "fontSize": 36}, "RawMessages!23k4q7q": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/lowstate.state_id", "fontSize": 36}, "Plot!20dw5kh": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[0].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[0].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[0].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[0].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[0].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[0].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[0].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[0].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Hip"}, "Plot!fayhsv": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[3].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[3].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[3].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[3].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[3].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[3].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[3].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[3].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Hip"}, "Plot!23qf7n7": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[6].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[6].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[6].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[6].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[6].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[6].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[6].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[6].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Hip"}, "Plot!45osfc7": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[9].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[9].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[9].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[9].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[9].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[9].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[9].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[9].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Hip"}, "Plot!1x3ukec": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[1].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[1].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[1].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[1].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[1].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[1].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[1].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[1].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Thigh"}, "Plot!2h0yztc": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[4].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[4].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[4].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[4].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[4].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[4].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[4].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[4].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Thigh"}, "Plot!1i09j0d": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[7].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[7].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[7].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[7].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[7].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[7].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[7].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[7].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Thigh"}, "Plot!wknpua": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[10].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[10].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[10].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[10].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[10].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[10].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[10].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[10].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Thigh"}, "Plot!14zausu": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[2].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[2].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[2].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[2].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[2].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[2].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[2].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[2].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FR-Calf"}, "Plot!vf3eu": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[5].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[5].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[5].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[5].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[5].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[5].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[5].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[5].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "FL-Calf"}, "Plot!3in8hf1": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[8].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[8].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[8].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[8].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[8].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[8].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[8].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[8].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RR-Calf"}, "Plot!q23nkn": {"paths": [{"timestampMethod": "receiveTime", "value": "/sim/lowcmd.motor_cmd[11].q", "enabled": false, "color": "#00FFFF", "label": "sim.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.relative_motor_cmd[11].q", "enabled": true, "color": "#4e98e2", "label": "sim.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[11].q", "enabled": true, "color": "#f5774d", "label": "sim.state.q"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[11].tau_est", "enabled": true, "color": "#f7df71", "label": "sim.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/sim/lowstate.motor_state[11].dq", "enabled": true, "color": "#FF00FF", "label": "sim.state.dq"}, {"timestampMethod": "receiveTime", "value": "/rl_lowcmd.motor_cmd[11].q", "enabled": false, "color": "#5cd6a9", "label": "real.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.relative_motor_cmd[11].q", "enabled": true, "color": "#a395e2", "label": "real.state.cmd.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].q", "enabled": true, "color": "#5de34f", "label": "real.state.q"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].tau_est", "enabled": true, "color": "#61cbff", "label": "real.state.tau_est"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.motor_state[11].dq", "enabled": true, "color": "#cad660", "label": "real.state.dq"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RL-Calf"}, "RawMessages!358xmf5": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rl_lowcmd", "fontSize": 12, "expansion": {"head": "e", "sn": "e", "version": "e", "motor_cmd": "c", "0~motor_cmd": "e", "reserve~0~motor_cmd": "e", "1~motor_cmd": "e", "reserve~1~motor_cmd": "e", "2~motor_cmd": "e", "reserve~2~motor_cmd": "e", "3~motor_cmd": "e", "reserve~3~motor_cmd": "e", "4~motor_cmd": "e", "reserve~4~motor_cmd": "e", "5~motor_cmd": "e", "reserve~5~motor_cmd": "e", "6~motor_cmd": "e", "reserve~6~motor_cmd": "e", "7~motor_cmd": "e", "reserve~7~motor_cmd": "e", "8~motor_cmd": "e", "reserve~8~motor_cmd": "e", "9~motor_cmd": "e", "reserve~9~motor_cmd": "e", "10~motor_cmd": "e", "reserve~10~motor_cmd": "e", "11~motor_cmd": "e", "reserve~11~motor_cmd": "e", "12~motor_cmd": "e", "reserve~12~motor_cmd": "e", "13~motor_cmd": "e", "reserve~13~motor_cmd": "e", "14~motor_cmd": "e", "reserve~14~motor_cmd": "e", "15~motor_cmd": "e", "reserve~15~motor_cmd": "e", "16~motor_cmd": "e", "reserve~16~motor_cmd": "e", "17~motor_cmd": "e", "reserve~17~motor_cmd": "e", "18~motor_cmd": "e", "reserve~18~motor_cmd": "e", "19~motor_cmd": "e", "reserve~19~motor_cmd": "e", "bms_cmd": "e", "reserve~bms_cmd": "e", "wireless_remote": "e", "led": "e", "fan": "e"}}, "RawMessages!3i8nnmj": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "/rt/lowstate", "fontSize": 12, "expansion": "none"}, "RawMessages!36s4opb": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "RawMessages!1iopdih": {"diffEnabled": false, "diffMethod": "custom", "diffTopicPath": "", "showFullMessageForDiff": false, "topicPath": "", "fontSize": 12}, "Plot!1cn9mfr": {"paths": [{"timestampMethod": "receiveTime", "value": "/joy.axes[$axes]", "enabled": true, "color": "#4e98e2"}, {"timestampMethod": "receiveTime", "value": "/joy.buttons[:]", "enabled": true, "color": "#f5774d"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240}, "GlobalVariableSliderPanel!41803xw": {"sliderProps": {"min": 0, "max": 5, "step": 1}, "globalVariableName": "axes"}, "Plot!2iqhnn4": {"paths": [{"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.temperature", "enabled": true, "color": "#4e98e2"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": false, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "temperature"}, "Plot!2yw5z2s": {"paths": [{"timestampMethod": "receiveTime", "value": "/proc/imu.acc.x", "enabled": true, "color": "#4e98e2", "label": "x"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.acc.y", "enabled": true, "color": "#f5774d", "label": "y"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.acc.z", "enabled": true, "color": "#f7df71", "label": "z"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.accelerometer[0]", "enabled": false, "color": "#5cd6a9", "label": "state.acc.x"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.accelerometer[1]", "enabled": false, "color": "#61cbff", "label": "state.acc.y"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.accelerometer[2]", "enabled": false, "color": "#a395e2", "label": "state.acc.z"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "acc"}, "Plot!1meezp8": {"paths": [{"timestampMethod": "receiveTime", "value": "/proc/imu.gyroscope.x", "enabled": true, "color": "#4e98e2", "label": "x"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.gyroscope.y", "enabled": true, "color": "#f5774d", "label": "y"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.gyroscope.z", "enabled": true, "color": "#f7df71", "label": "z"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.gyroscope[0]", "enabled": false, "color": "#5cd6a9", "label": "state.gyro.x"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.gyroscope[1]", "enabled": false, "color": "#61cbff", "label": "state.gyro.y"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.gyroscope[2]", "enabled": false, "color": "#a395e2", "label": "state.gyro.z"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "gyroscope"}, "Plot!pphcb2": {"paths": [{"timestampMethod": "receiveTime", "value": "/proc/imu.rpy.x", "enabled": true, "color": "#4e98e2", "label": "roll"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.rpy.y", "enabled": true, "color": "#f5774d", "label": "pitch"}, {"timestampMethod": "receiveTime", "value": "/proc/imu.rpy.z", "enabled": true, "color": "#f7df71", "label": "yaw"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "RPY"}, "Plot!7dv5zo": {"paths": [{"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.quaternion[0]", "enabled": true, "color": "#4e98e2", "label": "w"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.quaternion[1]", "enabled": true, "color": "#f5774d", "label": "x"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.quaternion[2]", "enabled": true, "color": "#f7df71", "label": "y"}, {"timestampMethod": "receiveTime", "value": "/rt/lowstate.imu_state.quaternion[3]", "enabled": true, "color": "#5cd6a9", "label": "z"}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "timestamp", "sidebarDimension": 240, "foxglovePanelTitle": "quat"}, "Plot!2ylj3er": {"paths": [{"timestampMethod": "receiveTime", "value": "/rt/odom.pose.pose.position.y", "enabled": true, "color": "#4e98e2", "xValuePath": "/rt/odom.pose.pose.position.x", "label": "trace", "showLine": true}], "showXAxisLabels": true, "showYAxisLabels": true, "showLegend": true, "legendDisplay": "floating", "showPlotValuesInLegend": false, "isSynced": true, "xAxisVal": "custom", "sidebarDimension": 240, "axisScalesMode": "lockedScales", "timeRange": "all", "foxglovePanelTitle": "<PERSON><PERSON>"}, "Tab!3hlxzed": {"activeTabIdx": 0, "tabs": [{"title": "Visualization", "layout": {"first": "DogLegSideView.DogLegSideView!2n3c7iq", "second": {"first": "RawMessages!1a537f6", "second": "RawMessages!2kwfjxn", "direction": "column"}, "direction": "row", "splitPercentage": 80.9114927344782}}, {"title": "Analysis", "layout": {"first": "Plot!4jizk9q", "second": {"first": "GlobalVariableSliderPanel!3nulfcx", "second": {"first": "RawMessages!iyhoq9", "second": "RawMessages!23k4q7q", "direction": "row"}, "direction": "row", "splitPercentage": 77.94063887196448}, "direction": "column", "splitPercentage": 83.35607094133697}}, {"title": "Dashboard", "layout": {"first": {"first": {"first": "Plot!20dw5kh", "second": "Plot!fayhsv", "direction": "column"}, "second": {"first": "Plot!23qf7n7", "second": "Plot!45osfc7", "direction": "column"}, "direction": "column"}, "second": {"first": {"first": {"first": "Plot!1x3ukec", "second": "Plot!2h0yztc", "direction": "column"}, "second": {"first": "Plot!1i09j0d", "second": "Plot!wknpua", "direction": "column"}, "direction": "column"}, "second": {"first": {"first": "Plot!14zausu", "second": "Plot!vf3eu", "direction": "column"}, "second": {"first": "Plot!3in8hf1", "second": "Plot!q23nkn", "direction": "column"}, "direction": "column"}, "direction": "row"}, "direction": "row", "splitPercentage": 31.047265987025025}}, {"title": "Message", "layout": {"first": {"first": "RawMessages!358xmf5", "second": "RawMessages!3i8nnmj", "direction": "row", "splitPercentage": 41.61646362797799}, "second": {"first": "RawMessages!36s4opb", "second": "RawMessages!1iopdih", "direction": "row", "splitPercentage": 41.57127991675338}, "direction": "column", "splitPercentage": 82.35786065960747}}, {"title": "<PERSON>", "layout": {"first": "Plot!1cn9mfr", "second": "GlobalVariableSliderPanel!41803xw", "direction": "column", "splitPercentage": 80.63366336633663}}, {"title": "IMU", "layout": {"first": {"first": "Plot!2<PERSON>hnn4", "second": {"first": "Plot!2yw5z2s", "second": "Plot!1meezp8", "direction": "column"}, "direction": "column", "splitPercentage": 33.3}, "second": {"first": "Plot!pphcb2", "second": "Plot!7dv5zo", "direction": "column"}, "direction": "row", "splitPercentage": 50}}, {"title": "<PERSON><PERSON>", "layout": "Plot!2ylj3er"}]}, "StateTransitions!2bzb504": {"paths": [{"value": "/rt/lowstate.relative_motor_cmd[0].kp", "timestampMethod": "receiveTime"}], "isSynced": true}, "StateTransitions!26srxzk": {"paths": [{"value": "/rt/lowstate.relative_motor_cmd[1].kp", "timestampMethod": "receiveTime"}], "isSynced": true}, "StateTransitions!55vlti": {"paths": [{"value": "/rt/lowstate.relative_motor_cmd[2].kp", "timestampMethod": "receiveTime"}], "isSynced": true}, "StateTransitions!2c5ckba": {"paths": [{"value": "/joy.axes[5]", "timestampMethod": "receiveTime", "label": "RT"}], "isSynced": true, "showPoints": true}}, "globalVariables": {"motor": 0, "axes": 1}, "userNodes": {"483a9f27-f665-4c3b-b22f-f11de0f2c37a": {"sourceCode": "// The ./types module provides helper types for your Input events and messages.\nimport { Input, Message } from \"./types\";\nimport { Vector3 } from \"@foxglove/schemas\";\n\n// Your script can output well-known message types, any of your custom message types, or\n// complete custom message types.\n//\n// Use `Message` to access types from the schemas defined in your data source:\n// type Twist = Message<\"geometry_msgs/Twist\">;\n//\n// Import from the @foxglove/schemas package to use foxglove schema types:\n// import { Pose, LocationFix } from \"@foxglove/schemas\";\n//\n// Conventionally, it's common to make a _type alias_ for your script's output type\n// and use that type name as the return type for your script function.\n// Here we've called the type `Output` but you can pick any type name.\ntype Output = {\n  rpy: Vector3;\n  acc: Vector3;\n  gyroscope: Vector3;\n};\n\n// These are the topics your script \"subscribes\" to. Foxglove will invoke your script function\n// when any message is received on one of these topics.\nexport const inputs = [\"/rt/lowstate\"];\n\n// Any output your script produces is \"published\" to this topic. Published messages are only visible within Foxglove, not to your original data source.\nexport const output = \"/proc/imu\";\n\nfunction quatToEuler(\n  w: number,\n  x: number,\n  y: number,\n  z: number,\n): { roll: number; pitch: number; yaw: number } {\n  // Roll (x-axis rotation)\n  const sinr_cosp = 2 * (w * x + y * z);\n  const cosr_cosp = 1 - 2 * (x * x + y * y);\n  const roll = Math.atan2(sinr_cosp, cosr_cosp);\n\n  // Pitch (y-axis rotation)\n  const sinp = 2 * (w * y - z * x);\n  const pitch = Math.asin(sinp);\n\n  // Yaw (z-axis rotation)\n  const siny_cosp = 2 * (w * z + x * y);\n  const cosy_cosp = 1 - 2 * (y * y + z * z);\n  const yaw = Math.atan2(siny_cosp, cosy_cosp);\n\n  // 转为角度\n  return {\n    roll: (roll * 180) / Math.PI,\n    pitch: (pitch * 180) / Math.PI,\n    yaw: (yaw * 180) / Math.PI,\n  };\n}\n\n// This function is called with messages from your input topics.\n// The first argument is an event with the topic, receive time, and message.\n// Use the `Input<...>` helper to get the correct event type for your input topic messages.\nexport default function script(event: Input<\"/rt/lowstate\">): Output {\n  const imu = event.message.imu_state;\n  const rpy = quatToEuler(\n    imu.quaternion[0],\n    imu.quaternion[1],\n    imu.quaternion[2],\n    imu.quaternion[3],\n  );\n  return {\n    rpy: {\n      x: rpy.roll,\n      y: rpy.pitch,\n      z: rpy.yaw,\n    },\n    acc: {\n      x: imu.accelerometer[0] * 9.81,\n      y: imu.accelerometer[1] * 9.81,\n      z: imu.accelerometer[2] * 9.81,\n    },\n    gyroscope: {\n      x: imu.gyroscope[0] * (Math.PI / 180),\n      y: imu.gyroscope[1] * (Math.PI / 180),\n      z: imu.gyroscope[2] * (Math.PI / 180),\n    },\n  };\n}\n", "name": "IMU"}}, "playbackConfig": {"speed": 1}, "layout": {"first": {"first": "Tab!3hlxzed", "second": {"first": {"first": "StateTransitions!2bzb504", "second": "StateTransitions!26srxzk", "direction": "row", "splitPercentage": 47.331319234642486}, "second": "StateTransitions!55vlti", "direction": "row", "splitPercentage": 65.5878467635403}, "direction": "column", "splitPercentage": 82.65582655826557}, "second": "StateTransitions!2c5ckba", "direction": "column", "splitPercentage": 85.7449088960343}}