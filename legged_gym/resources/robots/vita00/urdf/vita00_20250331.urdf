<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from vita00.xacro                   | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="vita00">
  <mujoco>
    <compiler balanceinertia="true" discardvisual="false" meshdir="../meshes/"/>
  </mujoco>
  <link name="base">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00775 0.0 0.0"/>
      <mass value="7.119"/>
      <inertia ixx="28807.764E-06" ixy="-676.862E-06" ixz="1592.964E-06" iyy="86135.021E-06" iyz="-232.235E-06" izz="100529.859E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/trunk.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="1.57079 0 1.57079" xyz="0.29866 0 0.0215"/>
      <geometry>
        <mesh filename="../meshes/Gemini_335.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="1.57079 -0.2618 3.1415926" xyz="0.22454 0 0.07165"/>
      <geometry>
        <mesh filename="../meshes/Livox_mid360.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0.075"/>
      <geometry>
        <mesh filename="../meshes/orinNX.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <box size="0.32 0.18 0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0.08"/>
      <geometry>
        <box size="0.089 0.118 0.06"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.12 0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="0.12 -0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.12 -0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.12 0.063 -0.07"/>
      <geometry>
        <box size="0.01 0.022 0.02"/>
      </geometry>
    </collision>
  </link>
 <link name="FL_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00249 0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="-1.271e-05" ixz="0.0" iyy="293.236E-06" iyz="0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.048"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.206 0.05 0"/>
    <parent link="base"/>
    <child link="FL_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.6632" upper="0.4538" velocity="37.7"/>
  </joint>
  <link name="FL_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00615 -0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="-0.000105" ixz="0.00045" iyy="6140E-06" iyz="-0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/thigh_left.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.4 0" xyz="0.022 0 -0.06"/>
      <geometry>
        <box size="0.004 0.04 0.1"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0  0.0893 0.0"/>
    <parent link="FL_hip"/>
    <child link="FL_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="3.14159" velocity="37.7"/>
  </joint>
  <link name="FL_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00209 0.0 -0.118722"/>
      <mass value="0.145"/>
      <inertia ixx="1143.834E-06" ixy="0.0E-06" ixz="-41.078E-06" iyy="1165.29E-06" iyz="0.0E-06" izz="34.028E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.4 0" xyz="-0.007 0 -0.05"/>
      <geometry>
        <cylinder length="0.1" radius="0.009"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.1 0" xyz="0.0185 0 -0.18"/>
      <geometry>
        <cylinder length="0.04" radius="0.011"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.14 0" xyz="0.0185 0 -0.128"/>
      <geometry>
        <box size="0.016 0.018 0.056"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="FL_thigh"/>
    <child link="FL_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.5236" velocity="21.8"/>
  </joint>
  <link name="FL_foot">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.04"/>
      <inertia ixx="9.6e-06" ixy="0" ixz="0" iyy="9.6e-06" iyz="0" izz="9.6e-06"/>
    </inertial>
    <collision>
      <origin rpy="0 0 0" xyz="-0.002 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint name="FL_foot_joint" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.22"/>
    <parent link="FL_calf"/>
    <child link="FL_foot"/>
    <axis xyz="0 0 0"/>
  </joint>
  <link name="FR_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00249 -0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="1.271e-05" ixz="0.0" iyy="293.236E-06" iyz="-0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.048"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.206 -0.05 0"/>
    <parent link="base"/>
    <child link="FR_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.4538" upper="0.6632" velocity="37.7"/>
  </joint>
  <link name="FR_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.00615 0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="0.000105" ixz="0.00045" iyy="6140E-06" iyz="0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/thigh_right.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.4 0" xyz="0.022 0 -0.06"/>
      <geometry>
        <box size="0.004 0.04 0.1"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0  -0.0893 0.0"/>
    <parent link="FR_hip"/>
    <child link="FR_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="3.14159" velocity="37.7"/>
  </joint>
  <link name="FR_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00209 0.0 -0.118722"/>
      <mass value="0.145"/>
      <inertia ixx="1143.834E-06" ixy="0.0E-06" ixz="-41.078E-06" iyy="1165.29E-06" iyz="0.0E-06" izz="34.028E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.4 0" xyz="-0.007 0 -0.05"/>
      <geometry>
        <cylinder length="0.1" radius="0.009"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.1 0" xyz="0.0185 0 -0.18"/>
      <geometry>
        <cylinder length="0.04" radius="0.011"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.14 0" xyz="0.0185 0 -0.128"/>
      <geometry>
        <box size="0.016 0.018 0.056"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="FR_thigh"/>
    <child link="FR_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.5236" velocity="21.8"/>
  </joint>
  <link name="FR_foot">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.04"/>
      <inertia ixx="9.6e-06" ixy="0" ixz="0" iyy="9.6e-06" iyz="0" izz="9.6e-06"/>
    </inertial>
    <collision>
      <origin rpy="0 0 0" xyz="-0.002 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint name="FR_foot_joint" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.22"/>
    <parent link="FR_calf"/>
    <child link="FR_foot"/>
    <axis xyz="0 0 0"/>
  </joint>
  <link name="RL_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00249 0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="1.271e-05" ixz="-0.0" iyy="293.236E-06" iyz="0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.048"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.206 0.05 0"/>
    <parent link="base"/>
    <child link="RL_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.6632" upper="0.4538" velocity="37.7"/>
  </joint>
  <link name="RL_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00615 -0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="0.000105" ixz="-0.00045" iyy="6140E-06" iyz="-0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/thigh_left.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.4 0" xyz="0.022 0 -0.06"/>
      <geometry>
        <box size="0.004 0.04 0.1"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0  0.0893 0.0"/>
    <parent link="RL_hip"/>
    <child link="RL_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="4.1713" velocity="37.7"/>
  </joint>
  <link name="RL_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00209 0.0 -0.118722"/>
      <mass value="0.145"/>
      <inertia ixx="1143.834E-06" ixy="0.0E-06" ixz="-41.078E-06" iyy="1165.29E-06" iyz="0.0E-06" izz="34.028E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.4 0" xyz="-0.007 0 -0.05"/>
      <geometry>
        <cylinder length="0.1" radius="0.009"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.1 0" xyz="0.0185 0 -0.18"/>
      <geometry>
        <cylinder length="0.04" radius="0.011"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.14 0" xyz="0.0185 0 -0.128"/>
      <geometry>
        <box size="0.016 0.018 0.056"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="RL_thigh"/>
    <child link="RL_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.5236" velocity="21.8"/>
  </joint>
  <link name="RL_foot">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.04"/>
      <inertia ixx="9.6e-06" ixy="0" ixz="0" iyy="9.6e-06" iyz="0" izz="9.6e-06"/>
    </inertial>
    <collision>
      <origin rpy="0 0 0" xyz="-0.002 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint name="RL_foot_joint" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.22"/>
    <parent link="RL_calf"/>
    <child link="RL_foot"/>
    <axis xyz="0 0 0"/>
  </joint>
  <link name="RR_hip">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00249 -0.00198 0.000018"/>
      <mass value="0.506"/>
      <inertia ixx="162.713E-06" ixy="-1.271e-05" ixz="-0.0" iyy="293.236E-06" iyz="-0.0" izz="258.577E-06"/>
    </inertial>
    <visual>
      <origin rpy="3.141592653589793 3.141592653589793 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/hip.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.5708 0 0" xyz="0 -0.08 0"/>
      <geometry>
        <cylinder length="0.04" radius="0.048"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_hip_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.206 -0.05 0"/>
    <parent link="base"/>
    <child link="RR_hip"/>
    <axis xyz="1 0 0"/>
    <limit effort="17" lower="-0.4538" upper="0.6632" velocity="37.7"/>
  </joint>
  <link name="RR_thigh">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00615 0.01668 -0.0405"/>
      <mass value="1.094"/>
      <inertia ixx="6155E-06" ixy="-0.000105" ixz="-0.00045" iyy="6140E-06" iyz="0.000724" izz="805E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/thigh_right.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 1.52 0" xyz="-0.029 0 -0.14"/>
      <geometry>
        <box size="0.186 0.023 0.025"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.4 0" xyz="0.022 0 -0.06"/>
      <geometry>
        <box size="0.004 0.04 0.1"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_thigh_joint" type="revolute">
    <origin rpy="0 0 0" xyz="-0.0  -0.0893 0.0"/>
    <parent link="RR_hip"/>
    <child link="RR_thigh"/>
    <axis xyz="0 1 0"/>
    <limit effort="17" lower="-0.87266" upper="4.1713" velocity="37.7"/>
  </joint>
  <link name="RR_calf">
    <inertial>
      <origin rpy="0 0 0" xyz="0.00209 0.0 -0.118722"/>
      <mass value="0.145"/>
      <inertia ixx="1143.834E-06" ixy="0.0E-06" ixz="-41.078E-06" iyy="1165.29E-06" iyz="0.0E-06" izz="34.028E-06"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/calf.STL"/>
      </geometry>
      <material name="">
        <color rgba="0.752941176470588 0.752941176470588 0.752941176470588 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 -0.4 0" xyz="-0.007 0 -0.05"/>
      <geometry>
        <cylinder length="0.1" radius="0.009"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0.1 0" xyz="0.0185 0 -0.18"/>
      <geometry>
        <cylinder length="0.04" radius="0.011"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 -0.14 0" xyz="0.0185 0 -0.128"/>
      <geometry>
        <box size="0.016 0.018 0.056"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_calf_joint" type="revolute">
    <origin rpy="0 0 0" xyz="0.0 0.0 -0.22"/>
    <parent link="RR_thigh"/>
    <child link="RR_calf"/>
    <axis xyz="0 1 0"/>
    <limit effort="29.4" lower="-2.7925" upper="-0.5236" velocity="21.8"/>
  </joint>
  <link name="RR_foot">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <mass value="0.04"/>
      <inertia ixx="9.6e-06" ixy="0" ixz="0" iyy="9.6e-06" iyz="0" izz="9.6e-06"/>
    </inertial>
    <collision>
      <origin rpy="0 0 0" xyz="-0.002 0 0"/>
      <geometry>
        <sphere radius="0.022"/>
      </geometry>
    </collision>
  </link>
  <joint name="RR_foot_joint" type="fixed" dont_collapse="true">
    <origin rpy="0 0 0" xyz="0 0 -0.22"/>
    <parent link="RR_calf"/>
    <child link="RR_foot"/>
    <axis xyz="0 0 0"/>
  </joint>
</robot>
