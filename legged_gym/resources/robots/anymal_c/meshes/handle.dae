<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.81.16 commit date:2019-12-04, commit time:11:32, hash:f1aa4d18d49d</authoring_tool>
    </contributor>
    <created>2020-02-13T20:44:07</created>
    <modified>2020-02-13T20:44:07</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_effects>
    <effect id="handle-effect">
      <profile_COMMON>
        <newparam sid="handle-surface">
          <surface type="2D">
            <init_from>handle</init_from>
          </surface>
        </newparam>
        <newparam sid="handle-sampler">
          <sampler2D>
            <source>handle-surface</source>
          </sampler2D>
        </newparam>
        <technique sid="common">
          <lambert>
            <emission>
              <color sid="emission">0 0 0 1</color>
            </emission>
            <diffuse>
              <texture texture="handle-sampler" texcoord="UVMap"/>
            </diffuse>
            <index_of_refraction>
              <float sid="ior">1.45</float>
            </index_of_refraction>
          </lambert>
        </technique>
      </profile_COMMON>
    </effect>
  </library_effects>
  <library_images>
    <image id="handle" name="handle">
      <init_from>handle.jpg</init_from>
    </image>
  </library_images>
  <library_materials>
    <material id="handle-material" name="handle">
      <instance_effect url="#handle-effect"/>
    </material>
  </library_materials>
  <library_geometries>
    <geometry id="Cube-mesh" name="Cube">
      <mesh>
        <source id="Cube-mesh-positions">
          <float_array id="Cube-mesh-positions-array" count="327">0.3486346 -0.04408091 0.1689196 0.3486346 -0.04676979 0.1729179 0.3486346 0.04408091 0.1689196 0.3486346 0.04676979 0.1729179 0.396654 -0.04408091 0.1689196 0.396654 -0.04676979 0.1729179 0.396654 0.04408091 0.1689196 0.396654 0.04676979 0.1729179 0.3251002 -0.06105065 0.1221223 0.3251002 -0.06483834 0.1215826 0.3251002 0.06483834 0.1215826 0.3251002 0.06105065 0.1221223 0.4038001 0.06105065 0.1221223 0.4038001 0.06483834 0.1215826 0.4038001 -0.06483834 0.1215826 0.4038001 -0.06105065 0.1221223 0.3090534 -0.06105065 0.07675099 0.3090534 -0.06468737 0.07300287 0.3090534 0.06468737 0.07300287 0.3090534 0.06105065 0.07675099 0.4088623 0.06105065 0.07675099 0.4088623 0.06468737 0.07300287 0.4088623 -0.06468737 0.07300287 0.4088623 -0.06105065 0.07675099 0.3090534 -0.04272723 0.07675099 0.3090534 -0.04272723 0.07300287 0.3090534 0.04272723 0.07300287 0.3090534 0.04272723 0.07675099 0.4088623 0.04272723 0.07675099 0.4088623 0.04272723 0.07300287 0.4088623 -0.04272723 0.07300287 0.4088623 -0.04272723 0.07675099 0.361269 0.05336838 0.1541703 0.3352567 -0.06105065 0.09700477 0.361269 -0.05336838 0.1541703 0.3466861 0.06105065 0.1221223 0.3464405 -0.06483834 0.1215826 0.361269 0.04942929 0.1541703 0.361269 -0.04942929 0.1541703 0.3352567 0.06105065 0.09700477 0.361269 -0.05336838 0.1541703 0.3464405 0.06483834 0.1215826 0.3352567 0.06476193 0.09700477 0.3352567 0.06105065 0.09700477 0.3352567 -0.06476199 0.09700477 0.3794677 0.05336838 0.1541703 0.3352567 -0.06105065 0.09700477 0.3352567 -0.06476199 0.09700477 0.3838461 0.06483834 0.1215826 0.3871484 0.06476193 0.09700477 0.3871484 0.06105065 0.09700477 0.3871484 0.06476193 0.09700477 0.3871484 -0.06105065 0.09700477 0.3871484 -0.06476199 0.09700477 0.3352567 0.06476193 0.09700477 0.3466861 -0.06105065 0.1221223 0.361269 0.05336838 0.1541703 0.3464405 -0.06483834 0.1215826 0.361269 0.04942929 0.1541703 0.3794677 0.04942929 0.1541703 0.3794677 0.05336838 0.1541703 0.3794677 0.04942929 0.1541703 0.3794677 -0.04942929 0.1541703 0.361269 -0.04942929 0.1541703 0.3794677 -0.05336838 0.1541703 0.3794677 -0.04942929 0.1541703 0.3464405 0.06483834 0.1215826 0.3794677 -0.05336838 0.1541703 0.3837736 0.06105065 0.1221223 0.3466861 0.06105065 0.1221223 0.3871484 0.06105065 0.09700477 0.3837736 -0.06105065 0.1221223 0.3837736 -0.06105065 0.1221223 0.3871484 -0.06105065 0.09700477 0.3838461 -0.06483834 0.1215826 0.3838461 -0.06483834 0.1215826 0.3871484 -0.06476199 0.09700477 0.3838461 0.06483834 0.1215826 0.3837736 0.06105065 0.1221223 0.3466861 -0.06105065 0.1221223 0.3565444 0.04681509 0.1613795 0.3566589 -0.04685467 0.1612703 0.3566589 -0.04685467 0.1612703 0.3887441 -0.04681509 0.1613795 0.388628 -0.04685527 0.1612688 0.3565444 0.04681509 0.1613795 0.3566606 0.04685527 0.1612688 0.388628 -0.04685527 0.1612688 0.388628 0.04685527 0.1612688 0.396654 0.04408091 0.1689196 0.3566606 -0.04685527 0.1612688 0.388628 0.04685527 0.1612688 0.3887441 -0.04681509 0.1613795 0.3566606 -0.04685527 0.1612688 0.3566606 0.04685527 0.1612688 0.3486346 -0.04408091 0.1689196 0.3565444 0.04681509 0.1613795 0.3566606 -0.04685527 0.1612688 0.3566606 0.04685527 0.1612688 0.3486346 0.04408091 0.1689196 0.396654 -0.04408091 0.1689196 0.3886296 0.04685467 0.1612703 0.3886296 0.04685467 0.1612703 0.396654 0.04408091 0.1689196 0.3887441 -0.04681509 0.1613795 0.388628 -0.04685527 0.1612688 0.388628 0.04685527 0.1612688 0.3886296 0.04685467 0.1612703 0.3486346 -0.04408091 0.1689196</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-positions-array" count="109" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-normals">
          <float_array id="Cube-mesh-normals-array" count="324">-1 0 0 0.9868565 -0.134097 0.0901786 1 0 0 4.69413e-7 0.9400991 -0.3409013 0.6953719 3.53562e-5 -0.7186501 0 0 1 0.9937095 -0.01578927 0.1108708 0 -0.9999952 -0.003108501 0 -0.9400994 -0.3409004 0 -0.9432769 0.3320072 0.98703 -0.02263027 0.1589335 0 0.9432765 0.3320081 -0.8815021 0.06660902 0.4674583 -0.8801726 0.3938707 0.2648811 0 0.9999952 -0.003107249 -0.9417109 0.04745113 0.3330603 0 1 0 0.9890567 0.1058899 0.1027343 0 -1 9.21415e-7 -0.9040334 -0.3067885 0.2976654 0 -1 0 0 0 -1 0 -3.78289e-6 1 0.9910943 0 0.1331619 0.9910942 0 0.1331623 -0.910199 0 0.4141715 -0.9101991 0 0.4141713 0.6899862 0 -0.7238225 -0.6933983 0 -0.7205545 2.22169e-7 -0.9400985 -0.3409032 -2.22169e-7 0.9400989 -0.3409019 -0.6899849 0 -0.7238238 0.98703 0.02263027 0.1589335 -7.88001e-7 0.9400991 -0.3409013 0.003526926 0.9401479 -0.3407483 0 0.9400994 -0.3409004 0 0.9400992 -0.340901 0 0.9400995 -0.3409003 0 0.940099 -0.3409015 -7.03433e-5 0.9400674 -0.3409889 1.03237e-6 0.9400989 -0.3409022 0 0.9400998 -0.3408995 0.6899839 4.48615e-7 -0.7238249 0.6900208 0 -0.7237896 0.9890567 -0.1058899 0.1027343 2.592e-7 -0.9999952 -0.00310868 0 -0.9999952 -0.003108382 0 -0.9999952 -0.003107964 -2.725e-7 -0.9999952 -0.003108739 0 -0.9999952 -0.003108739 3.8161e-7 -0.9400996 -0.3409 -0.001604497 -0.9384663 -0.3453672 0 -0.9400995 -0.3409003 0 -0.9400991 -0.3409016 6.39902e-7 -0.9400994 -0.3409004 1.74353e-4 -0.9401137 -0.340861 0 -0.9400991 -0.3409013 -7.99407e-7 -0.9400995 -0.3409003 0 -0.940099 -0.3409015 2.01526e-7 -0.9432767 0.3320079 0 -0.943277 0.3320072 0 -0.9432768 0.3320074 -2.16115e-7 -0.9432769 0.3320071 0 -0.9432765 0.3320081 0.9868565 0.134097 0.0901786 2.16115e-7 0.9432769 0.3320071 0 0.9432768 0.3320072 0 0.9432766 0.332008 -2.01526e-7 0.9432767 0.3320078 0 0.9432769 0.3320072 -0.8801726 -0.3938707 0.2648811 -0.8815021 -0.06660902 0.4674583 5.42952e-7 0.9999952 -0.00310862 0 0.9999952 -0.003108859 0 0.9999952 -0.003109097 -5.16451e-7 0.9999952 -0.003108441 0 0.9999952 -0.003108918 -0.9040334 0.3067885 0.2976654 0.9937095 0.01578927 0.1108708 -1.91491e-7 -1 0 0 -1 -6.36444e-7 0 -1 -4.29431e-7 3.91312e-7 -1 0 0 -1 -4.43065e-7 -0.9417109 -0.04745113 0.3330603 0 -3.7829e-6 1 0.9910943 0 0.1331619 0.9910942 0 0.1331628 0.9910943 0 0.1331616 0.9910944 0 0.1331617 0.9910943 -2.72867e-6 0.1331619 0.9910942 1.04533e-6 0.1331624 -0.9101988 0 0.4141718 -0.9101991 -1.44603e-5 0.4141713 -0.9101988 0 0.414172 -0.9101987 0 0.4141719 -0.9101992 7.23942e-6 0.4141709 -0.9101991 0 0.4141714 -1.25918e-7 0 -1 -0.6899839 0 -0.7238249 -0.6901179 -2.87415e-7 -0.723697 -1.80409e-7 0 -1 0 -0.9400996 -0.3409 0 -0.9401062 -0.3408818 0 -0.9414574 -0.337132 0 0.9400997 -0.3408997 2.22734e-7 0.9400666 -0.3409909 2.74934e-7 0.9400196 -0.3411207</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-normals-array" count="108" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Cube-mesh-map">
          <float_array id="Cube-mesh-map-array" count="852">0.327175 0.0548706 0.3152982 0.3307656 0.3152982 0.0630362 0.6235113 0.0630362 0.4769862 0 0.6357384 0.0548706 0.6357384 0.0548706 0.6235113 0.3307657 0.6235113 0.0630362 0.6596832 0.06144738 0.7822008 0.01537287 0.6818996 0.0892654 0.4077661 0.6970229 0.4081223 0.9814806 0.4081174 0.9814789 0 0.9809615 0.1473258 0.696901 0.1473258 0.9809615 0.4769862 0.3938018 0.3386371 0.3822993 0.4786366 0.3822993 0.2291391 0.4733759 0.3026569 0.3938018 0.3026569 0.6969009 0.6759932 0.6076355 0.6537768 0.6354535 0.6537768 0.5383749 0 0.6598269 0 0.5140016 0.0561884 0.6076355 0.6235113 0.3307657 0.4769862 0.3938018 0.4786366 0.3822993 0.3833635 0.4830672 0.3833635 0.538333 0.327175 0.5767012 0.3152982 0.3307656 0.1566216 0.3938018 0.1582247 0.3822993 0.3152982 0.0630362 0.1566216 0 0.327175 0.0548706 0.3026569 1 0.2464399 0.6969009 0.3026568 0.6969009 0.3386372 0.01150256 0.3271751 0.06714695 0.3271751 4.58604e-4 0.5563142 0.6173269 0.5563142 0.4597426 0.6298319 0.3938018 0.1566216 0.3938018 0.01113373 0.3822993 0.1582247 0.3822993 0.8591215 0.223525 0.8591215 0.06594073 0.9211471 0 0.4769862 0 0.3386372 0.01150256 0.3271751 4.58604e-4 0.853215 0.4733759 0.9152406 0.3938018 0.9152406 0.6969009 0.1566216 0 0.01113373 0.01150256 0 4.58604e-4 0.9940935 0.3938018 0.9826152 0.6969009 0.9826152 0.3938018 1 0 0.9885216 0.3030991 0.9885216 0 0.01113373 0.01150256 0 0.06714695 0 4.58604e-4 0.9152406 0.3938018 0.9826152 0.6969009 0.9152407 0.6969009 0.3386371 0.3822993 0.327175 0.3266549 0.3386372 0.3266549 0.921147 0 0.9885216 0.3030991 0.9211471 0.3030991 0.01113373 0.3822993 0 0.3266549 0.01113373 0.3266549 0.3026569 0.6969009 0.3588739 1 0.302657 1 0.659909 0.303099 0.6478236 0.3583648 0.6478236 0.303099 0.6357384 0.303099 0.6478236 0.3583648 0.6357384 0.3583648 0.5027018 0.8000037 0.5180584 0.8753396 0.4916403 0.8038972 0.5291198 0.774466 0.5326299 0.8752028 0.5180584 0.7705724 0.4762437 0.8069138 0.4916403 0.8880288 0.4651533 0.8107805 0.4428831 0.7801949 0.4531602 0.8875744 0.4315717 0.7770175 0.5558629 0.6969009 0.5672494 0.8544852 0.5558629 0.8544853 0.5444764 0.6969009 0.555863 0.8544852 0.5444764 0.8544853 0.4081223 0.7072553 0.4315716 0.6969009 0.4315716 0.9739767 0.3834981 0.6969009 0.3834981 0.9814806 0.3834933 0.9814789 0.2454034 0.6969009 0.1473258 0.9814806 0.1473258 0.6969009 0.6537721 0.6354583 0.629832 0.6598269 0.6534304 0.5380223 0.6596785 0.1585308 0.6357384 0.1828994 0.6593368 0.06109482 0 0 0 0 0 0 0.3037257 0.4041562 0.327175 0.3938018 0.327175 0.6708777 0.327175 0.0548706 0.327175 0.3389312 0.3152982 0.3307656 0.6235113 0.0630362 0.4786366 0.01150256 0.4769862 0 0.6357384 0.0548706 0.6357383 0.3389312 0.6235113 0.3307657 0.7822008 0.2543684 0.6357384 0.1828994 0.6596785 0.1585308 0.7822008 0.2543684 0.6596785 0.1585308 0.6596832 0.158526 0.6818996 0.1445311 0.7822008 0.1888165 0.6596832 0.158526 0.7822008 0.1888165 0.7822008 0.2543684 0.6596832 0.158526 0.7822008 0.01537287 0.7822008 0.07618916 0.6818996 0.0892654 0.6818996 0.0892654 0.6818996 0.1445311 0.6596832 0.158526 0.6596832 0.06144738 0.6593368 0.06109482 0.7822008 0.01537287 0.6593368 0.06109482 0.6357384 0.03707402 0.7822008 0.01537287 0.6818996 0.0892654 0.6596832 0.158526 0.6596832 0.06144738 0.4081174 0.9814789 0.3834981 0.9730555 0.4077661 0.6970229 0.4077661 0.6970229 0.4081223 0.6969009 0.4081223 0.9814806 0 0.9809615 0 0.6969009 0.1473258 0.696901 0.4769862 0.3938018 0.327175 0.3933432 0.3386371 0.3822993 0.3026569 0.6969009 0.153857 0.681528 0.2291391 0.6309602 0.153857 0.681528 0.153857 0.6209319 0.2291391 0.6309602 0.153857 0.5073387 0.153857 0.4425325 0.2291391 0.4733759 0.153857 0.4425325 0.3026569 0.3938018 0.2291391 0.4733759 0.3026569 0.6969009 0.2291391 0.6309602 0.2291391 0.4733759 0.7762944 0.681528 0.629832 0.6598269 0.6537721 0.6354583 0.7762944 0.681528 0.6537721 0.6354583 0.6537768 0.6354535 0.6759932 0.6076355 0.7762944 0.6207118 0.7762944 0.681528 0.7762944 0.4425325 0.7762944 0.5080844 0.6534304 0.5380223 0.7762944 0.5080844 0.6759932 0.5523698 0.6534304 0.5380223 0.6534304 0.5380223 0.6759932 0.5523698 0.6537768 0.5383749 0.6759932 0.6076355 0.7762944 0.681528 0.6537768 0.6354535 0.629832 0.5140016 0.7762944 0.4425325 0.6534304 0.5380223 0.6759932 0.5523698 0.6759932 0.6076355 0.6537768 0.5383749 0 0.5140016 0.153857 0.4425325 0.0561884 0.5523698 0.153857 0.4425325 0.153857 0.5073387 0.0561884 0.5523698 0.153857 0.6209319 0.153857 0.681528 0.0561884 0.6076355 0.153857 0.681528 0 0.6598269 0.0561884 0.6076355 0 0.5140016 0.0561884 0.5523698 0.0561884 0.6076355 0.6235113 0.3307657 0.6357383 0.3389312 0.4769862 0.3938018 0.327175 0.4308758 0.481032 0.4091747 0.3833635 0.4830672 0.481032 0.4091747 0.481032 0.4697708 0.3833635 0.4830672 0.4810321 0.5833641 0.4810321 0.6481702 0.3833635 0.538333 0.4810321 0.6481702 0.327175 0.5767012 0.3833635 0.538333 0.327175 0.4308758 0.3833635 0.4830672 0.327175 0.5767012 0.3152982 0.3307656 0.327175 0.3389312 0.1566216 0.3938018 0.3152982 0.0630362 0.1582247 0.01150256 0.1566216 0 0.3026569 1 0.2464399 1 0.2464399 0.6969009 0.3386372 0.01150256 0.3386372 0.06714695 0.3271751 0.06714695 0.629832 0.6969009 0.4810321 0.6481702 0.5563142 0.6173269 0.4810321 0.6481702 0.4810321 0.5833641 0.5563142 0.6173269 0.481032 0.4697708 0.481032 0.4091747 0.5563142 0.4597426 0.481032 0.4091747 0.6298319 0.3938018 0.5563142 0.4597426 0.629832 0.6969009 0.5563142 0.6173269 0.6298319 0.3938018 0.1566216 0.3938018 0 0.3933432 0.01113373 0.3822993 0.921147 0.303099 0.7822008 0.2543684 0.8591215 0.223525 0.7822008 0.2543684 0.7822008 0.1888165 0.8591215 0.223525 0.7822008 0.07618916 0.7822008 0.01537287 0.8591215 0.06594073 0.7822008 0.01537287 0.9211471 0 0.8591215 0.06594073 0.921147 0.303099 0.8591215 0.223525 0.9211471 0 0.4769862 0 0.4786366 0.01150256 0.3386372 0.01150256 0.9152406 0.6969009 0.7762944 0.681528 0.853215 0.6309602 0.7762944 0.681528 0.7762944 0.6207118 0.853215 0.6309602 0.7762944 0.5080844 0.7762944 0.4425325 0.853215 0.4733759 0.7762944 0.4425325 0.9152406 0.3938018 0.853215 0.4733759 0.9152406 0.6969009 0.853215 0.6309602 0.853215 0.4733759 0.1566216 0 0.1582247 0.01150256 0.01113373 0.01150256 0.9940935 0.3938018 0.9940936 0.6969009 0.9826152 0.6969009 1 0 1 0.3030991 0.9885216 0.3030991 0.01113373 0.01150256 0.01113373 0.06714695 0 0.06714695 0.9152406 0.3938018 0.9826152 0.3938018 0.9826152 0.6969009 0.3386371 0.3822993 0.327175 0.3933432 0.327175 0.3266549 0.921147 0 0.9885215 0 0.9885216 0.3030991 0.01113373 0.3822993 0 0.3933432 0 0.3266549 0.3026569 0.6969009 0.3588739 0.6969009 0.3588739 1 0.659909 0.303099 0.659909 0.3583648 0.6478236 0.3583648 0.6357384 0.303099 0.6478236 0.303099 0.6478236 0.3583648 0.5180584 0.8753396 0.5068969 0.8775687 0.4916403 0.8038972 0.4916403 0.8038972 0.5062118 0.6992668 0.5027018 0.8000037 0.5062118 0.6992668 0.5180584 0.6969009 0.5027018 0.8000037 0.5333148 0.6969009 0.5444764 0.69913 0.5180584 0.7705724 0.5444764 0.69913 0.5291198 0.774466 0.5180584 0.7705724 0.5291198 0.774466 0.5444764 0.8775687 0.5326299 0.8752028 0.4916403 0.8880288 0.4804496 0.8901098 0.4651533 0.8107805 0.4651533 0.8107805 0.4797629 0.6991097 0.4762437 0.8069138 0.4797629 0.6991097 0.4916403 0.6969009 0.4762437 0.8069138 0.4418606 0.6969009 0.4531602 0.69829 0.4315717 0.7770175 0.4531602 0.69829 0.4428831 0.7801949 0.4315717 0.7770175 0.4428831 0.7801949 0.4651533 0.8890488 0.4531602 0.8875744 0.5558629 0.6969009 0.5672494 0.6969009 0.5672494 0.8544852 0.5444764 0.6969009 0.555863 0.6969009 0.555863 0.8544852 0.3834933 0.9814789 0.3588739 0.9730555 0.3831419 0.6970228 0.3831419 0.6970228 0.3834981 0.6969009 0.3834933 0.9814789 0.2454034 0.6969009 0.2454033 0.9814807 0.1473258 0.9814806 0.629832 0.6598269 0.629832 0.5140016 0.6534304 0.5380223 0.6534304 0.5380223 0.6537768 0.5383749 0.6537721 0.6354583 0.6537768 0.5383749 0.6537768 0.6354535 0.6537721 0.6354583 0.6357384 0.1828994 0.6357384 0.03707402 0.6593368 0.06109482 0.6593368 0.06109482 0.6596832 0.06144738 0.6596785 0.1585308 0.6596832 0.06144738 0.6596832 0.158526 0.6596785 0.1585308 0 0 0 0 0 0</float_array>
          <technique_common>
            <accessor source="#Cube-mesh-map-array" count="426" stride="2">
              <param name="S" type="float"/>
              <param name="T" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Cube-mesh-vertices">
          <input semantic="POSITION" source="#Cube-mesh-positions"/>
        </vertices>
        <lines count="2">
          <input semantic="VERTEX" source="#Cube-mesh-vertices" offset="0"/>
          <p>82 0 82 90</p>
        </lines>
        <triangles material="handle-material" count="142">
          <input semantic="VERTEX" source="#Cube-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Cube-mesh-normals" offset="1"/>
          <input semantic="TEXCOORD" source="#Cube-mesh-map" offset="2" set="0"/>
          <p>1 0 0 2 0 1 0 0 2 6 1 3 13 1 4 7 1 5 7 2 6 4 2 7 6 2 8 84 3 9 15 3 10 62 3 11 104 4 12 106 4 13 107 4 14 3 5 15 5 5 16 7 5 17 14 6 18 23 6 19 15 6 20 47 7 21 17 7 22 22 7 23 61 8 24 91 8 25 86 8 26 5 9 27 1 9 28 64 9 29 4 10 30 14 10 31 15 10 32 60 11 33 32 11 34 3 11 35 2 12 36 10 12 37 11 12 38 0 13 39 9 13 40 1 13 41 19 5 42 28 5 43 20 5 44 20 2 45 29 2 46 21 2 47 42 14 48 49 14 49 21 14 50 10 15 51 19 15 52 11 15 53 46 16 54 52 16 55 23 16 56 13 17 57 20 17 58 21 17 59 43 18 60 19 18 61 20 18 62 9 19 63 16 19 64 17 19 65 26 20 66 28 20 67 27 20 68 30 16 69 24 16 70 31 16 71 16 0 72 25 0 73 17 0 74 17 21 75 30 21 76 22 21 77 23 2 78 30 2 79 31 2 80 21 21 81 26 21 82 18 21 83 19 0 84 26 0 85 27 0 86 23 5 87 24 5 88 16 5 89 45 5 90 58 5 91 59 5 92 63 22 93 67 22 94 65 22 95 68 23 96 70 23 97 48 23 98 71 24 99 67 24 100 75 24 101 79 25 102 33 25 103 36 25 104 35 26 105 56 26 106 41 26 107 51 21 108 39 21 109 54 21 110 73 21 111 44 21 112 33 21 113 100 27 114 92 27 115 89 27 116 98 28 117 93 28 118 82 28 119 88 21 120 97 21 121 94 21 122 101 29 123 6 29 124 85 29 125 81 30 126 0 30 127 83 30 128 101 5 129 102 5 130 6 5 131 99 31 132 80 31 133 108 31 134 1 0 135 3 0 136 2 0 137 6 32 138 12 32 139 13 32 140 7 2 141 5 2 142 4 2 143 8 33 144 0 33 145 81 33 146 8 34 147 81 34 148 90 34 149 38 35 150 55 35 151 90 35 152 55 36 153 8 36 154 90 36 155 15 37 156 72 37 157 62 37 158 62 38 159 38 38 160 90 38 161 84 39 162 83 39 163 15 39 164 83 40 165 4 40 166 15 40 167 62 41 168 90 41 169 84 41 170 107 42 171 103 42 172 104 42 173 104 43 174 105 43 175 106 43 176 3 5 177 1 5 178 5 5 179 14 44 180 22 44 181 23 44 182 22 45 183 14 45 184 53 45 185 14 46 186 74 46 187 53 46 188 57 47 189 9 47 190 47 47 191 9 48 192 17 48 193 47 48 194 22 49 195 53 49 196 47 49 197 12 50 198 6 50 199 102 50 200 12 51 201 102 51 202 91 51 203 61 52 204 78 52 205 12 52 206 11 53 207 69 53 208 85 53 209 69 54 210 37 54 211 85 54 212 85 55 213 37 55 214 86 55 215 61 56 216 12 56 217 91 56 218 2 57 219 11 57 220 85 57 221 37 58 222 61 58 223 86 58 224 1 59 225 9 59 226 40 59 227 9 60 228 57 60 229 40 60 230 74 61 231 14 61 232 64 61 233 14 62 234 5 62 235 64 62 236 1 63 237 40 63 238 64 63 239 4 64 240 5 64 241 14 64 242 7 65 243 13 65 244 60 65 245 13 66 246 77 66 247 60 66 248 66 67 249 10 67 250 32 67 251 10 68 252 3 68 253 32 68 254 7 69 255 60 69 256 3 69 257 2 70 258 3 70 259 10 70 260 0 71 261 8 71 262 9 71 263 19 5 264 27 5 265 28 5 266 20 2 267 28 2 268 29 2 269 18 72 270 10 72 271 42 72 272 10 73 273 66 73 274 42 73 275 77 74 276 13 74 277 49 74 278 13 75 279 21 75 280 49 75 281 18 76 282 42 76 283 21 76 284 10 77 285 18 77 286 19 77 287 16 16 288 8 16 289 46 16 290 8 16 291 55 16 292 46 16 293 72 16 294 15 16 295 52 16 296 15 16 297 23 16 298 52 16 299 16 16 300 46 16 301 23 16 302 13 78 303 12 78 304 20 78 305 20 79 306 12 79 307 50 79 308 12 80 309 78 80 310 50 80 311 69 81 312 11 81 313 43 81 314 11 82 315 19 82 316 43 82 317 20 83 318 50 83 319 43 83 320 9 84 321 8 84 322 16 84 323 26 20 324 29 20 325 28 20 326 30 16 327 25 16 328 24 16 329 16 0 330 24 0 331 25 0 332 17 21 333 25 21 334 30 21 335 23 2 336 22 2 337 30 2 338 21 21 339 29 21 340 26 21 341 19 0 342 18 0 343 26 0 344 23 5 345 31 5 346 24 5 347 45 5 348 56 5 349 58 5 350 63 85 351 34 85 352 67 85 353 70 86 354 51 86 355 48 86 356 48 87 357 45 87 358 68 87 359 45 88 360 59 88 361 68 88 362 76 89 363 73 89 364 75 89 365 73 90 366 71 90 367 75 90 368 71 91 369 65 91 370 67 91 371 33 92 372 44 92 373 36 92 374 36 93 375 34 93 376 79 93 377 34 94 378 63 94 379 79 94 380 54 95 381 39 95 382 41 95 383 39 96 384 35 96 385 41 96 386 35 97 387 58 97 388 56 97 389 51 98 390 70 98 391 39 98 392 73 21 393 76 21 394 44 21 395 82 99 396 95 99 397 96 99 398 96 100 399 98 100 400 82 100 401 88 101 402 87 101 403 97 101 404 6 102 405 2 102 406 85 102 407 85 103 408 86 103 409 101 103 410 86 104 411 91 104 412 101 104 413 0 105 414 4 105 415 83 105 416 83 106 417 84 106 418 81 106 419 84 107 420 90 107 421 81 107 422 101 5 423 91 5 424 102 5 425</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Cube" name="Cube" type="NODE">
        <matrix sid="transform">1 0 0 0 0 1 0 0 0 0 1 0 0 0 0 1</matrix>
        <instance_geometry url="#Cube-mesh" name="Cube">
          <bind_material>
            <technique_common>
              <instance_material symbol="handle-material" target="#handle-material">
                <bind_vertex_input semantic="UVMap" input_semantic="TEXCOORD" input_set="0"/>
              </instance_material>
            </technique_common>
          </bind_material>
        </instance_geometry>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>