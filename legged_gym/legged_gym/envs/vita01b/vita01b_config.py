# SPDX-FileCopyrightText: Copyright (c) 2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: BSD-3-Clause
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are met:
#
# 1. Redistributions of source code must retain the above copyright notice, this
# list of conditions and the following disclaimer.
#
# 2. Redistributions in binary form must reproduce the above copyright notice,
# this list of conditions and the following disclaimer in the documentation
# and/or other materials provided with the distribution.
#
# 3. Neither the name of the copyright holder nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
# AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
# IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
# DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
# FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
# DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
# SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
# CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
# OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
#
# Copyright (c) 2021 ETH Zurich, Nikita Rudin

from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg, LeggedRobotCfgPPO

class Vita01bRoughCfg( LeggedRobotCfg ):
    # # [yujun.zhang] 添加高摩擦力地形配置
    # class terrain( LeggedRobotCfg.terrain ):
    #     # 设置非常高的摩擦力系数来模拟高阻力路面
    #     static_friction = 5.0      # 静态摩擦系数 (默认1.0)
    #     dynamic_friction = 4.0     # 动态摩擦系数 (默认1.0)
    #     restitution = 0.0          # 保持弹性系数为0
    
    class init_state( LeggedRobotCfg.init_state ):
        pos = [0.0, 0.0, 0.42] # x,y,z [m]
        # default_joint_angles = { # = target angles [rad] when action = 0.0  default  height=0.311
        #     'FL_hip_joint': 0.1,   # [rad]
        #     'RL_hip_joint': 0.1,   # [rad]
        #     'FR_hip_joint': -0.1 ,  # [rad]
        #     'RR_hip_joint': -0.1,   # [rad]

        #     'FL_thigh_joint': 0.8,     # [rad]
        #     'RL_thigh_joint': 1.,   # [rad]
        #     'FR_thigh_joint': 0.8,     # [rad]
        #     'RR_thigh_joint': 1.,   # [rad]

        #     'FL_calf_joint': -1.5,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.5,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }
        # default_joint_angles = {  # = target angles [rad] when action = 0.0  HIM  height=0.35
        #     'FL_hip_joint': 0.0,  # [rad]
        #     'RL_hip_joint': 0.0,  # [rad]
        #     'FR_hip_joint': -0.0,  # [rad]
        #     'RR_hip_joint': -0.0,  # [rad]

        #     'FL_thigh_joint': 0.6,  # [rad]
        #     'RL_thigh_joint': 0.65,  # [rad]
        #     'FR_thigh_joint': 0.6,  # [rad]
        #     'RR_thigh_joint': 0.65,  # [rad]

        #     'FL_calf_joint': -1.2,  # [rad]
        #     'RL_calf_joint': -1.3,  # [rad]
        #     'FR_calf_joint': -1.2,  # [rad]
        #     'RR_calf_joint': -1.3  # [rad]
        # }

        # default_joint_angles = {  # = target angles [rad] when action = 0.0  HIM  height=0.35
        #     'FL_hip_joint': 0.0,  # [rad]
        #     'RL_hip_joint': 0.0,  # [rad]
        #     'FR_hip_joint': -0.0,  # [rad]
        #     'RR_hip_joint': -0.0,  # [rad]

        #     'FL_thigh_joint': 0.65,  # [rad]
        #     'RL_thigh_joint': 0.65,  # [rad]
        #     'FR_thigh_joint': 0.65,  # [rad]
        #     'RR_thigh_joint': 0.65,  # [rad]

        #     'FL_calf_joint': -1.3,  # [rad]
        #     'RL_calf_joint': -1.3,  # [rad]
        #     'FR_calf_joint': -1.3,  # [rad]
        #     'RR_calf_joint': -1.3  # [rad]
        # }

        # # # NP3O
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.1,   # [rad]
        #     'RL_hip_joint': 0.1,   # [rad]
        #     'FR_hip_joint': -0.1 ,  # [rad]
        #     'RR_hip_joint': -0.1,   # [rad]

        #     'FL_thigh_joint': 0.8,     # [rad]
        #     'RL_thigh_joint': 1.,   # [rad]
        #     'FR_thigh_joint': 0.8,     # [rad]
        #     'RR_thigh_joint': 1.,   # [rad]

        #     'FL_calf_joint': -1.5,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.5,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # [shiqi.tan]
        # default_joint_angles = {  # = target angles [rad] when action = 0.0  HIM-go2  height=0.363/0.35
        #     'FL_hip_joint': 0.0,  # [rad]
        #     'RL_hip_joint': 0.0,  # [rad]
        #     'FR_hip_joint': -0.0,  # [rad]
        #     'RR_hip_joint': -0.0,  # [rad]

        #     'FL_thigh_joint': 0.6,  # [rad]
        #     'RL_thigh_joint': 0.65,  # [rad]
        #     'FR_thigh_joint': 0.6,  # [rad]
        #     'RR_thigh_joint': 0.65,  # [rad]

        #     'FL_calf_joint': -1.3,  # [rad]
        #     'RL_calf_joint': -1.2,  # [rad]
        #     'FR_calf_joint': -1.3,  # [rad]
        #     'RR_calf_joint': -1.2  # [rad]
        # }

        # # # yujun.zhang
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.1,   # [rad]
        #     'RL_hip_joint': 0.1,   # [rad]
        #     'FR_hip_joint': -0.1 ,  # [rad]
        #     'RR_hip_joint': -0.1,   # [rad]

        #     'FL_thigh_joint': 0.6,  # [rad]
        #     'RL_thigh_joint': 0.65,  # [rad]
        #     'FR_thigh_joint': 0.6,  # [rad]
        #     'RR_thigh_joint': 0.65,  # [rad]

        #     'FL_calf_joint': -1.5,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.5,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # # target pos1
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.1,   # [rad]
        #     'RL_hip_joint': 0.1,   # [rad]
        #     'FR_hip_joint': -0.1 ,  # [rad]
        #     'RR_hip_joint': -0.1,   # [rad]

        #     'FL_thigh_joint': 0.65,  # [rad]
        #     'RL_thigh_joint': 0.7,  # [rad]
        #     'FR_thigh_joint': 0.65,  # [rad]
        #     'RR_thigh_joint': 0.7,  # [rad]

        #     'FL_calf_joint': -1.5,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.5,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # target pos2
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.0,   # [rad]
        #     'RL_hip_joint': 0.0,   # [rad]
        #     'FR_hip_joint': -0.0 ,  # [rad]
        #     'RR_hip_joint': -0.0,   # [rad]

        #     'FL_thigh_joint': 0.65,  # [rad]
        #     'RL_thigh_joint': 0.7,  # [rad]
        #     'FR_thigh_joint': 0.65,  # [rad]
        #     'RR_thigh_joint': 0.7,  # [rad]

        #     'FL_calf_joint': -1.4,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.4,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # # target pos3
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.1,   # [rad]
        #     'RL_hip_joint': 0.1,   # [rad]
        #     'FR_hip_joint': -0.1 ,  # [rad]
        #     'RR_hip_joint': -0.1,   # [rad]

        #     'FL_thigh_joint': 0.6,  # [rad]
        #     'RL_thigh_joint': 0.7,  # [rad]
        #     'FR_thigh_joint': 0.6,  # [rad]
        #     'RR_thigh_joint': 0.7,  # [rad]

        #     'FL_calf_joint': -1.4,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.4,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # # # target pos4
        # default_joint_angles = { # = target angles [rad] when action = 0.0
        #     'FL_hip_joint': 0.0,   # [rad]
        #     'RL_hip_joint': 0.0,   # [rad]
        #     'FR_hip_joint': -0.0 ,  # [rad]
        #     'RR_hip_joint': -0.0,   # [rad]

        #     'FL_thigh_joint': 0.6,  # [rad]
        #     'RL_thigh_joint': 0.7,  # [rad]
        #     'FR_thigh_joint': 0.6,  # [rad]
        #     'RR_thigh_joint': 0.7,  # [rad]

        #     'FL_calf_joint': -1.4,   # [rad]
        #     'RL_calf_joint': -1.5,    # [rad]
        #     'FR_calf_joint': -1.4,  # [rad]
        #     'RR_calf_joint': -1.5,    # [rad]
        # }

        # # # target pos5
        default_joint_angles = { # = target angles [rad] when action = 0.0
            'FL_hip_joint': 0.04,   # [rad]
            'RL_hip_joint': 0.04,   # [rad]
            'FR_hip_joint': -0.04 ,  # [rad]
            'RR_hip_joint': -0.04,   # [rad]

            'FL_thigh_joint': 0.6,  # [rad]
            'RL_thigh_joint': 0.7,  # [rad]
            'FR_thigh_joint': 0.6,  # [rad]
            'RR_thigh_joint': 0.7,  # [rad]

            'FL_calf_joint': -1.4,   # [rad]
            'RL_calf_joint': -1.5,    # [rad]
            'FR_calf_joint': -1.4,  # [rad]
            'RR_calf_joint': -1.5,    # [rad]
        }



    class control( LeggedRobotCfg.control ):
        # PD Drive parameters:
        control_type = 'P'  # 'actuator_net' or 'P'
        # stiffness = {'joint': 40.0}  # [N*m/rad]
        # damping = {'joint': 1.0}     # [N*m*s/rad]
        # stiffness = {'hip_joint': 40.0, 'thigh_joint': 40.0, 'calf_joint': 40.0}  # [N*m/rad]
        # damping = {'hip_joint': 1.0, 'thigh_joint': 1.0, 'calf_joint': 1.0}     # [N*m*s/rad]
        stiffness = {'hip_joint': 30.0, 'thigh_joint': 30.0, 'calf_joint': 30.0}  # [N*m/rad]
        damping = {'hip_joint': 1.0, 'thigh_joint': 1.0, 'calf_joint': 1.0}     # [N*m*s/rad]
        # action scale: target angle = actionScale * action + defaultAngle
        action_scale = 0.25
        # decimation: Number of control action updates @ sim DT per policy DT
        decimation = 4
        hip_reduction = 0.5
        actuator_net_name = 'vita_00_him_kp30_kd1_22_56_03_0.pt'
        ang_vel_smoothing_ratio = 0.2  # [0.0, 1.0], 1.0: no smoothing, 0.0: no change

    class commands( LeggedRobotCfg.commands ):
            curriculum = True
            max_curriculum = 1.2
            # max_curriculum = 0.7
            # max_forward_curriculum = 1.5
            # max_backward_curriculum = 1.0
            # max_lat_curriculum = 1.0


            num_commands = 5 # default: lin_vel_x, lin_vel_y, ang_vel_yaw, heading (in heading mode ang_vel_yaw is recomputed from heading error)
            resampling_time = 10. # time before command are changed[s]
            heading_command = True # if true: compute ang vel command from heading error
            class ranges( LeggedRobotCfg.commands.ranges):
                lin_vel_x = [-1.0, 1.0] # min max [m/s]
                # lin_vel_x = [-0.3, 0.3] # min max [m/s]
                lin_vel_y = [-0.3, 0.3]   # min max [m/s]
                # lin_vel_x = [-0.5, 0.5]  # min max [m/s]
                # lin_vel_y = [-0.5, 0.5]  # min max [m/s]
                ang_vel_yaw = [-1.0, 1.0]    # min max [rad/s]
                heading = [-3.14, 3.14]
            # high_vel_ratio = 0.2
            heading_ratio = 0.98

    class asset( LeggedRobotCfg.asset ):
        file = '{LEGGED_GYM_ROOT_DIR}/resources/robots/vitamin/src/robot/vita01b/urdf/vita01b_0719.urdf'
        name = "vita01b"
        foot_name = "foot"
        penalize_contacts_on = ["thigh", "calf", "base"]
        terminate_after_contacts_on = ["base"]
        # terminate_after_contacts_on = []
        privileged_contacts_on = ["base", "thigh", "calf"]
        self_collisions = 1 # 1 to disable, 0 to enable...bitwise filter
        # self_collisions = 0 # 1 to disable, 0 to enable...bitwise filter
        flip_visual_attachments = False # Some .obj meshes must be flipped from y-up to z-up

        # # new armature
        dof_armature = 0.01
        # dof_armature_calf = 0.045
        dof_armature_calf = 0.04
        friction = 0.01
        damping = 0.01

    class domain_rand( LeggedRobotCfg.domain_rand ):
        # randomize_motor_offset = True
        # motor_offset_range = [-0.02, 0.02]

        disturbance = True
        disturbance_range = [-500.0, 500.0]
        # disturbance_range = [-30.0, 30.0]
        disturbance_interval = 8

        foot_disturbance = True
        foot_disturbance_range = [-100.0, 100.0]
        foot_disturbance_interval = 8

        randomize_gravity = False
        gravity_range = [-1.0, 1.0]
        gravity_rand_interval_s = 8.0
        gravity_impulse_duration = 0.25

        randomize_lag_timesteps = True
        lag_random_durations = 2
        # lag_timesteps = 4
        lag_timesteps = 5

        # add code for randomize
        randomize_armature = True
        armature_range = [0.5, 1.5]
        calf_armature_range = [0.5, 1.5]

        # # [yujun.zhang] 启用地形摩擦力随机化，设置高摩擦力范围
        # randomize_friction = True
        # friction_range = [0.2, 8.0]  # 原来是[0.2, 1.25]，现在设置为高摩擦力范围

        randomize_dof_friction = True
        dof_friction_range = [0.5, 1.5]
        # dof_friction_range = [0.01, 0.10]

        randomize_dof_damping = True
        dof_damping_range = [0.5, 1.5]

        # payload_mass_range = [-2, 1]


    # --------- compare to here --------
    class rewards( LeggedRobotCfg.rewards ):
        class scales:
            termination = -0.0
            tracking_lin_vel = 2.0
            tracking_ang_vel = 1.0
            lin_vel_z = -2.0
            ang_vel_xy = -0.05
            orientation = -0.2
            # orientation = -1.0
            dof_acc = -2.5e-7
            # dof_acc = -1e-6
            joint_power = -2e-5
            # base_height = -0.0
            base_height = -10.0
            # foot_clearance = -1.0
            foot_clearance = -0.0
            action_rate = -0.01
            smoothness = -0.01
            # smoothness = -0.0
            # feet_air_time =  5.0
            feet_air_time =  0.0
            collision = -1.0
            # stumble = -0.2
            # stumble = -0.1
            stumble = -1.0
            stand_still = -0.
            torques = -0.0
            dof_vel = -0.0
            # dof_pos_limits = -1.0
            # dof_vel_limits = -2e-5
            # torque_limits = -2e-5
            dof_pos = -0.1
            # dof_pos = -0.0
            hip_pos = -0.5
            # hip_pos = -0.0
            # feet_slip = -0.0
            feet_slip = -0.5
            # feet_impact_vel = -1.0
            # feet_contact_forces = -1.0
            # calf_pos = -0.5
            feet_impact_vel = -0.1
            # feet_impact_vel = -0.5
            # feet_contact_forces = -0.0
            feet_contact_forces = -0.01
            thigh_pos = -0.0
            # thigh_pos = -0.0
            stance_control = 0.5
            # stance_control = 0.0

            # add code
            stand_nice = -0.1
            # stand_nice = -0.0
            # foot_mirror = -0.05
            foot_mirror = -0.0
            # has_contact = 1.0   
            has_contact = 0.0
            # terrain_adaptation = -3.0
            terrain_adaptation = -0.0

            torque_balance = 0.5
            orientation_y = -0.1
            # orientation_y = -0.0

            # sin_footheight = -0.5
            sin_footheight = -0.7
            # sin_footheight = -0.9

        only_positive_rewards = False # if true negative total rewards are clipped at zero (avoids early termination problems)
        # only_positive_rewards = True # if true negative total rewards are clipped at zero (avoids early termination problems)
        tracking_sigma = 0.25 # tracking reward = exp(-error^2/sigma)
        soft_dof_pos_limit = 1.0 # percentage of urdf limits, values above this limit are penalized
        soft_dof_vel_limit = 0.9
        soft_torque_limit = 0.9
        # soft_torque_limit = 0.8
        # base_height_target = 0.30
        base_height_target = 0.32
        max_contact_force = 100. # forces above this value are penalized
        clearance_height_target = -0.17
        only_positive_rewards_exp_style = False
        sigma_rew_neg = 0.02
    
    # add code
    class costs:
        class scales:
            pos_limit = 0.1
            torque_limit = 0.1
            dof_vel_limits = 0.1
            

        class d_values:
            pos_limit = 0.0
            torque_limit = 0.0
            dof_vel_limits = 0.0
    
    class cost:
        num_costs = 3



class Vita01bRoughCfgPPO( LeggedRobotCfgPPO ):
    class algorithm( LeggedRobotCfgPPO.algorithm ):
        entropy_coef = 0.01
    class runner( LeggedRobotCfgPPO.runner ):
        run_name = ''
        experiment_name = 'rough_vita01b'




