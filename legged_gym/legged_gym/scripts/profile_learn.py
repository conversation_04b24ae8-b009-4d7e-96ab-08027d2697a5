from line_profiler import LineProfiler


import isaacgym
from legged_gym.envs import *
from legged_gym.utils import get_args, task_registry
import torch
# 假设你有env, train_cfg, log_dir, device等参数
# runner = HIMOnPolicyRunner(env, train_cfg, log_dir, device)

def profile_learn(args):
    env, env_cfg = task_registry.make_env(name=args.task, args=args)
    runner, train_cfg = task_registry.make_alg_runner(env=env, name=args.task, args=args)
    
    lp = LineProfiler()
    lp.add_function(runner.learn)
    # 例如：lp_wrapper = lp(runner.learn)
    # 假设你要分析100次迭代
    lp_wrapper = lp(runner.learn)
    lp_wrapper(10, init_at_random_ep_len=False)
    lp.print_stats()

if __name__ == "__main__":
    args = get_args()
    args.task = 'vita01'
    device = 'cuda:1'
    args.rl_device = device
    args.sim_device = device
    args.headless = True
    args.max_iterations = 10000
    profile_learn(args)